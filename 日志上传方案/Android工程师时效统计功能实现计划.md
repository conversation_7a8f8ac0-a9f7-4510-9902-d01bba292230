# Android工程师时效统计功能实现计划

## 1. 项目概述

### 1.1 功能目标
基于后台API接口文档，在现有Android项目中集成工程师时效统计功能，包括：
- 工程师月度时效统计数据展示
- 工程师月度时效明细数据查看
- 数据筛选、搜索和分页加载
- 友好的用户界面和交互体验

### 1.2 技术架构分析
**现有架构模式：** MVVM (Model-View-ViewModel)
**网络层：** Retrofit2 + OkHttp + Gson
**数据层：** Repository模式 + Room数据库
**UI层：** Fragment + RecyclerView + ViewBinding
**异步处理：** Kotlin协程 + LiveData

## 2. API接口分析

### 2.1 月度统计接口
- **路径：** `/statistics/queryEngineerMonthTimelinessList`
- **方法：** POST
- **功能：** 获取工程师月度时效汇总数据
- **支持：** 分页查询、时间范围筛选、工程师名称搜索

### 2.2 月度明细接口
- **路径：** `/statistics/queryEngineerMonthTimelinessDetailsList`
- **方法：** POST
- **功能：** 获取工程师某月的详细工单时效数据
- **支持：** 分页查询、工单编号搜索、客户名称搜索

### 2.3 数据结构特点
- 统一的响应格式：`{code, message, data}`
- 标准分页结构：`{total, pageNum, pageSize, pages, list}`
- 丰富的时效指标：接单、备料、路上、维修、确认时间
- 评价数据：专业评分、服务态度评分

## 3. 详细实现计划

### 3.1 第一阶段：数据模型层 (1-2天)

#### 3.1.1 请求参数模型
```kotlin
// 月度统计查询参数
data class MonthTimelinessQuery(
    val pageNum: Int = 1,
    val pageSize: Int = 20,
    val startMonth: String? = null,  // yyyy-MM格式
    val endMonth: String? = null,    // yyyy-MM格式
    val name: String? = null         // 工程师名称模糊查询
)

// 月度明细查询参数
data class TimelinessDetailsQuery(
    val pageNum: Int = 1,
    val pageSize: Int = 20,
    val engineerId: Long,            // 必填：工程师ID
    val monthly: String,             // 必填：月份 yyyy-MM格式
    val code: String? = null,        // 工单编号模糊查询
    val name: String? = null         // 客户名称模糊查询
)
```

#### 3.1.2 响应数据模型
```kotlin
// 月度统计数据
data class MonthTimelinessVO(
    val monthly: String,                    // 月份
    val engineerName: String,               // 工程师名称
    val engineerId: Long,                   // 工程师ID
    val orderNums: Int,                     // 工单总数
    val receiveNum: Int,                    // 接单数量
    // 平均时间指标（分钟）
    val receiveTimeAvg: Double,             // 平均接单时间
    val prepareTimeAvg: Double,             // 平均备料时间
    val onRoadTimeAvg: Double,              // 平均路上时间
    val repairTimeAvg: Double,              // 平均维修时间
    val confirmTimeAvg: Double,             // 平均确认时间
    // 平均评价指标
    val professionalEvaluateAvg: Double,    // 平均专业评分
    val serviceEvaluateAvg: Double,         // 平均服务态度评分
    // 累计时间指标（分钟）
    val receiveTime: Int,                   // 接单时间总和
    val prepareTime: Int,                   // 备料时间总和
    val onRoadTime: Int,                    // 路上时间总和
    val repairTime: Int,                    // 维修时间总和
    val confirmTime: Int,                   // 确认时间总和
    // 累计评价指标
    val professionalEvaluate: Int,          // 专业评分总和
    val serviceEvaluate: Int                // 服务态度评分总和
)

// 明细数据
data class TimelinessDetailVO(
    val id: Long,                           // 统计记录ID
    val workOrderId: Long,                  // 工单ID
    val code: String,                       // 工单编号
    val productId: Long,                    // 产品ID
    val engineerId: Long,                   // 工程师ID
    val engineerName: String,               // 工程师名称
    val monthly: String,                    // 月份
    val name: String,                       // 客户名称
    // 时间指标（分钟）
    val receiveTime: Int,                   // 接单时间
    val prepareTime: Int,                   // 备料时间
    val onRoadTime: Int,                    // 路上时间
    val repairTime: Int,                    // 维修时间
    val confirmTime: Int,                   // 确认时间
    val receiveNum: Int,                    // 接单数量
    // 评价指标
    val professionalEvaluate: Int,          // 专业评分(1-5)
    val serviceEvaluate: Int,               // 服务态度评分(1-5)
    val evaluateTime: String,               // 评价时间(ISO格式)
    // 系统字段
    val createTime: String,                 // 创建时间(ISO格式)
    val updateTime: String,                 // 更新时间(ISO格式)
    val productInfo: String                 // 产品信息
)
```

#### 3.1.3 筛选状态模型
```kotlin
// 统计筛选状态
data class StatisticsFilterState(
    val startMonth: String? = null,
    val endMonth: String? = null,
    val engineerName: String? = null,
    val sortBy: SortType = SortType.MONTHLY_DESC
)

// 明细筛选状态
data class DetailFilterState(
    val workOrderCode: String? = null,
    val customerName: String? = null,
    val sortBy: DetailSortType = DetailSortType.CREATE_TIME_DESC
)

enum class SortType {
    MONTHLY_ASC, MONTHLY_DESC,
    ORDER_COUNT_ASC, ORDER_COUNT_DESC,
    AVG_REPAIR_TIME_ASC, AVG_REPAIR_TIME_DESC
}

enum class DetailSortType {
    CREATE_TIME_ASC, CREATE_TIME_DESC,
    REPAIR_TIME_ASC, REPAIR_TIME_DESC,
    EVALUATE_SCORE_ASC, EVALUATE_SCORE_DESC
}
```

### 3.2 第二阶段：网络层实现 (2-3天)

#### 3.2.1 API服务接口
```kotlin
interface StatisticsService {
    
    /**
     * 获取工程师月度时效统计列表
     */
    @POST("statistics/queryEngineerMonthTimelinessList")
    suspend fun getMonthTimelinessList(
        @Body query: MonthTimelinessQuery
    ): Response<ApiResponse<PageData<MonthTimelinessVO>>>
    
    /**
     * 获取工程师月度时效明细列表
     */
    @POST("statistics/queryEngineerMonthTimelinessDetailsList")
    suspend fun getTimelinessDetailsList(
        @Body query: TimelinessDetailsQuery
    ): Response<ApiResponse<PageData<TimelinessDetailVO>>>
}
```

#### 3.2.2 Repository实现
```kotlin
class StatisticsRepository : BaseNetworkRepository() {
    
    private val api = ApiClient.createService<StatisticsService>()
    
    /**
     * 获取月度统计数据
     */
    suspend fun getMonthStatistics(
        query: MonthTimelinessQuery
    ): ApiResult<PageData<MonthTimelinessVO>> {
        return executeWithRetry(
            request = { api.getMonthTimelinessList(query) },
            metricName = "statistics_month_list"
        )
    }
    
    /**
     * 获取明细数据
     */
    suspend fun getTimelinessDetails(
        query: TimelinessDetailsQuery
    ): ApiResult<PageData<TimelinessDetailVO>> {
        return executeWithRetry(
            request = { api.getTimelinessDetailsList(query) },
            metricName = "statistics_detail_list"
        )
    }
}
```

### 3.3 第三阶段：业务逻辑层 (3-4天)

#### 3.3.1 统计ViewModel
```kotlin
class StatisticsViewModel : ViewModel() {
    
    private val repository = StatisticsRepository()
    
    // UI状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _isRefreshing = MutableLiveData<Boolean>()
    val isRefreshing: LiveData<Boolean> = _isRefreshing
    
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    
    // 数据
    private val _statisticsList = MutableLiveData<List<MonthTimelinessVO>>()
    val statisticsList: LiveData<List<MonthTimelinessVO>> = _statisticsList
    
    // 分页状态
    private var currentPage = 1
    private var isLastPage = false
    private var isLoadingMore = false
    
    // 筛选状态
    private var filterState = StatisticsFilterState()
    
    /**
     * 加载统计数据
     */
    fun loadStatistics(refresh: Boolean = false) {
        if (refresh) {
            currentPage = 1
            isLastPage = false
            _isRefreshing.value = true
        } else {
            _isLoading.value = true
        }
        
        viewModelScope.launch {
            try {
                val query = MonthTimelinessQuery(
                    pageNum = currentPage,
                    pageSize = 20,
                    startMonth = filterState.startMonth,
                    endMonth = filterState.endMonth,
                    name = filterState.engineerName
                )
                
                when (val result = repository.getMonthStatistics(query)) {
                    is ApiResult.Success -> {
                        val pageData = result.data
                        val newList = pageData.list ?: emptyList()
                        
                        if (refresh) {
                            _statisticsList.value = newList
                        } else {
                            val currentList = _statisticsList.value ?: emptyList()
                            _statisticsList.value = currentList + newList
                        }
                        
                        isLastPage = currentPage >= pageData.pages
                        currentPage++
                    }
                    is ApiResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "加载失败"
            } finally {
                _isLoading.value = false
                _isRefreshing.value = false
                isLoadingMore = false
            }
        }
    }
    
    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (!isLoadingMore && !isLastPage) {
            isLoadingMore = true
            loadStatistics()
        }
    }
    
    /**
     * 应用筛选条件
     */
    fun applyFilter(newFilterState: StatisticsFilterState) {
        filterState = newFilterState
        loadStatistics(refresh = true)
    }
    
    /**
     * 搜索工程师
     */
    fun searchEngineer(name: String) {
        filterState = filterState.copy(engineerName = name.takeIf { it.isNotBlank() })
        loadStatistics(refresh = true)
    }
}
```

#### 3.3.2 明细ViewModel
```kotlin
class StatisticsDetailViewModel : ViewModel() {

    private val repository = StatisticsRepository()

    // UI状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 数据
    private val _detailsList = MutableLiveData<List<TimelinessDetailVO>>()
    val detailsList: LiveData<List<TimelinessDetailVO>> = _detailsList

    // 分页状态
    private var currentPage = 1
    private var isLastPage = false
    private var isLoadingMore = false

    // 筛选状态
    private var filterState = DetailFilterState()
    private var engineerId: Long = 0
    private var monthly: String = ""

    /**
     * 初始化数据
     */
    fun initialize(engineerId: Long, monthly: String) {
        this.engineerId = engineerId
        this.monthly = monthly
        loadDetails(refresh = true)
    }

    /**
     * 加载明细数据
     */
    fun loadDetails(refresh: Boolean = false) {
        if (refresh) {
            currentPage = 1
            isLastPage = false
        }

        _isLoading.value = true

        viewModelScope.launch {
            try {
                val query = TimelinessDetailsQuery(
                    pageNum = currentPage,
                    pageSize = 20,
                    engineerId = engineerId,
                    monthly = monthly,
                    code = filterState.workOrderCode,
                    name = filterState.customerName
                )

                when (val result = repository.getTimelinessDetails(query)) {
                    is ApiResult.Success -> {
                        val pageData = result.data
                        val newList = pageData.list ?: emptyList()

                        if (refresh) {
                            _detailsList.value = newList
                        } else {
                            val currentList = _detailsList.value ?: emptyList()
                            _detailsList.value = currentList + newList
                        }

                        isLastPage = currentPage >= pageData.pages
                        currentPage++
                    }
                    is ApiResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "加载失败"
            } finally {
                _isLoading.value = false
                isLoadingMore = false
            }
        }
    }

    /**
     * 搜索功能
     */
    fun search(workOrderCode: String?, customerName: String?) {
        filterState = filterState.copy(
            workOrderCode = workOrderCode?.takeIf { it.isNotBlank() },
            customerName = customerName?.takeIf { it.isNotBlank() }
        )
        loadDetails(refresh = true)
    }
}
```

### 3.4 第四阶段：UI层实现 (4-6天)

#### 3.4.1 工具类实现
```kotlin
object TimeFormatUtils {

    /**
     * 将分钟转换为友好的时间格式显示
     */
    fun formatMinutes(minutes: Int): String {
        if (minutes <= 0) return "0分钟"

        val hours = minutes / 60
        val mins = minutes % 60

        return when {
            hours > 0 && mins > 0 -> "${hours}小时${mins}分钟"
            hours > 0 -> "${hours}小时"
            else -> "${mins}分钟"
        }
    }

    /**
     * 格式化ISO时间为显示格式
     */
    fun formatISOTime(isoTime: String): String {
        return try {
            val input = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
            val output = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            val date = input.parse(isoTime)
            output.format(date ?: Date())
        } catch (e: ParseException) {
            isoTime
        }
    }

    /**
     * 获取月份显示格式
     */
    fun formatMonth(month: String): String {
        return try {
            val input = SimpleDateFormat("yyyy-MM", Locale.getDefault())
            val output = SimpleDateFormat("yyyy年MM月", Locale.getDefault())
            val date = input.parse(month)
            output.format(date ?: Date())
        } catch (e: ParseException) {
            month
        }
    }
}
```

#### 3.4.2 主统计界面
```kotlin
class StatisticsFragment : Fragment() {

    private lateinit var binding: FragmentStatisticsBinding
    private lateinit var viewModel: StatisticsViewModel
    private lateinit var adapter: StatisticsAdapter
    private val searchHandler = Handler(Looper.getMainLooper())
    private var searchRunnable: Runnable? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentStatisticsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupViewModel()
        setupRecyclerView()
        setupSearchView()
        setupSwipeRefresh()
        setupFilterButton()
        observeData()

        // 初始加载数据
        viewModel.loadStatistics(refresh = true)
    }

    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[StatisticsViewModel::class.java]
    }

    private fun setupRecyclerView() {
        adapter = StatisticsAdapter { statisticsItem ->
            // 点击跳转到明细页面
            navigateToDetail(statisticsItem.engineerId, statisticsItem.monthly, statisticsItem.engineerName)
        }

        binding.recyclerView.adapter = adapter
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // 添加分页加载监听
        binding.recyclerView.addOnScrollListener(object : PaginationScrollListener(
            binding.recyclerView.layoutManager as LinearLayoutManager
        ) {
            override fun loadMoreItems() {
                viewModel.loadMore()
            }

            override fun isLastPage(): Boolean = viewModel.isLastPage
            override fun isLoading(): Boolean = viewModel.isLoadingMore
        })
    }

    private fun setupSearchView() {
        binding.searchView.apply {
            setIconifiedByDefault(false)
            queryHint = "搜索工程师名称"

            setOnQueryTextListener(object : SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    query?.let { viewModel.searchEngineer(it) }
                    return true
                }

                override fun onQueryTextChange(newText: String?): Boolean {
                    // 实现搜索防抖
                    searchHandler.removeCallbacks(searchRunnable ?: return true)
                    searchRunnable = Runnable {
                        newText?.let { viewModel.searchEngineer(it) }
                    }
                    searchHandler.postDelayed(searchRunnable!!, 500)
                    return true
                }
            })
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadStatistics(refresh = true)
        }
    }

    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }
    }

    private fun observeData() {
        viewModel.statisticsList.observe(viewLifecycleOwner) { list ->
            adapter.submitList(list)
            updateEmptyState(list.isEmpty())
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.isVisible = isLoading && !binding.swipeRefreshLayout.isRefreshing
        }

        viewModel.isRefreshing.observe(viewLifecycleOwner) { isRefreshing ->
            binding.swipeRefreshLayout.isRefreshing = isRefreshing
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { message ->
            if (message.isNotEmpty()) {
                Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG).show()
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        binding.layoutEmpty.isVisible = isEmpty && !binding.progressBar.isVisible
        binding.recyclerView.isVisible = !isEmpty
    }

    private fun showFilterDialog() {
        val dialog = StatisticsFilterDialog()
        dialog.setOnFilterAppliedListener { filterState ->
            viewModel.applyFilter(filterState)
        }
        dialog.show(parentFragmentManager, "filter_dialog")
    }

    private fun navigateToDetail(engineerId: Long, monthly: String, engineerName: String) {
        val action = StatisticsFragmentDirections
            .actionStatisticsToDetail(engineerId, monthly, engineerName)
        findNavController().navigate(action)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        searchHandler.removeCallbacks(searchRunnable ?: return)
    }
}
```

#### 3.4.3 统计列表适配器
```kotlin
class StatisticsAdapter(
    private val onItemClick: (MonthTimelinessVO) -> Unit
) : ListAdapter<MonthTimelinessVO, StatisticsAdapter.ViewHolder>(StatisticsDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemStatisticsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemStatisticsBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(item: MonthTimelinessVO) {
            binding.apply {
                // 基础信息
                tvEngineerName.text = item.engineerName
                tvMonthly.text = TimeFormatUtils.formatMonth(item.monthly)
                tvOrderCount.text = "${item.orderNums}单"
                tvReceiveCount.text = "接单${item.receiveNum}单"

                // 平均时间指标
                tvAvgReceiveTime.text = TimeFormatUtils.formatMinutes(item.receiveTimeAvg.toInt())
                tvAvgPrepareTime.text = TimeFormatUtils.formatMinutes(item.prepareTimeAvg.toInt())
                tvAvgOnRoadTime.text = TimeFormatUtils.formatMinutes(item.onRoadTimeAvg.toInt())
                tvAvgRepairTime.text = TimeFormatUtils.formatMinutes(item.repairTimeAvg.toInt())
                tvAvgConfirmTime.text = TimeFormatUtils.formatMinutes(item.confirmTimeAvg.toInt())

                // 评价指标
                tvProfessionalScore.text = String.format("%.1f分", item.professionalEvaluateAvg)
                tvServiceScore.text = String.format("%.1f分", item.serviceEvaluateAvg)

                // 设置评分颜色
                setScoreColor(tvProfessionalScore, item.professionalEvaluateAvg)
                setScoreColor(tvServiceScore, item.serviceEvaluateAvg)

                // 设置进度条
                setTimeProgressBars(item)
            }
        }

        private fun setScoreColor(textView: TextView, score: Double) {
            val color = when {
                score >= 4.5 -> ContextCompat.getColor(itemView.context, R.color.score_excellent)
                score >= 4.0 -> ContextCompat.getColor(itemView.context, R.color.score_good)
                score >= 3.5 -> ContextCompat.getColor(itemView.context, R.color.score_average)
                else -> ContextCompat.getColor(itemView.context, R.color.score_poor)
            }
            textView.setTextColor(color)
        }

        private fun setTimeProgressBars(item: MonthTimelinessVO) {
            // 计算总时间用于设置进度条比例
            val totalTime = item.receiveTimeAvg + item.prepareTimeAvg +
                           item.onRoadTimeAvg + item.repairTimeAvg + item.confirmTimeAvg

            if (totalTime > 0) {
                binding.progressReceive.progress = ((item.receiveTimeAvg / totalTime) * 100).toInt()
                binding.progressPrepare.progress = ((item.prepareTimeAvg / totalTime) * 100).toInt()
                binding.progressOnRoad.progress = ((item.onRoadTimeAvg / totalTime) * 100).toInt()
                binding.progressRepair.progress = ((item.repairTimeAvg / totalTime) * 100).toInt()
                binding.progressConfirm.progress = ((item.confirmTimeAvg / totalTime) * 100).toInt()
            }
        }
    }
}

class StatisticsDiffCallback : DiffUtil.ItemCallback<MonthTimelinessVO>() {
    override fun areItemsTheSame(oldItem: MonthTimelinessVO, newItem: MonthTimelinessVO): Boolean {
        return oldItem.engineerId == newItem.engineerId && oldItem.monthly == newItem.monthly
    }

    override fun areContentsTheSame(oldItem: MonthTimelinessVO, newItem: MonthTimelinessVO): Boolean {
        return oldItem == newItem
    }
}
```

### 3.5 第五阶段：筛选和搜索功能 (2-3天)

#### 3.5.1 筛选对话框
```kotlin
class StatisticsFilterDialog : DialogFragment() {

    private lateinit var binding: DialogStatisticsFilterBinding
    private var onFilterAppliedListener: ((StatisticsFilterState) -> Unit)? = null
    private var currentFilterState = StatisticsFilterState()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        binding = DialogStatisticsFilterBinding.inflate(layoutInflater)

        setupViews()
        setupListeners()

        return AlertDialog.Builder(requireContext())
            .setTitle("筛选条件")
            .setView(binding.root)
            .setPositiveButton("应用") { _, _ ->
                applyFilter()
            }
            .setNegativeButton("取消", null)
            .setNeutralButton("重置") { _, _ ->
                resetFilter()
            }
            .create()
    }

    private fun setupViews() {
        // 设置月份选择器
        setupMonthPickers()

        // 设置工程师名称输入
        binding.etEngineerName.setText(currentFilterState.engineerName ?: "")

        // 设置排序选项
        setupSortOptions()
    }

    private fun setupMonthPickers() {
        // 开始月份选择
        binding.btnStartMonth.setOnClickListener {
            showMonthPicker { month ->
                currentFilterState = currentFilterState.copy(startMonth = month)
                binding.btnStartMonth.text = TimeFormatUtils.formatMonth(month)
            }
        }

        // 结束月份选择
        binding.btnEndMonth.setOnClickListener {
            showMonthPicker { month ->
                currentFilterState = currentFilterState.copy(endMonth = month)
                binding.btnEndMonth.text = TimeFormatUtils.formatMonth(month)
            }
        }

        // 显示当前选择的月份
        currentFilterState.startMonth?.let {
            binding.btnStartMonth.text = TimeFormatUtils.formatMonth(it)
        }
        currentFilterState.endMonth?.let {
            binding.btnEndMonth.text = TimeFormatUtils.formatMonth(it)
        }
    }

    private fun setupSortOptions() {
        val sortOptions = arrayOf(
            "月份降序", "月份升序",
            "工单数量降序", "工单数量升序",
            "平均维修时间降序", "平均维修时间升序"
        )

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, sortOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerSort.adapter = adapter

        // 设置当前选择
        binding.spinnerSort.setSelection(currentFilterState.sortBy.ordinal)
    }

    private fun showMonthPicker(onMonthSelected: (String) -> Unit) {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)

        val monthPicker = MonthPickerDialog.Builder(requireContext())
            .setActivatedMonth(month)
            .setMinYear(year - 2)
            .setMaxYear(year)
            .setActivatedYear(year)
            .setOnMonthChangedListener { selectedMonth, selectedYear ->
                val monthStr = String.format("%04d-%02d", selectedYear, selectedMonth + 1)
                onMonthSelected(monthStr)
            }
            .build()

        monthPicker.show()
    }

    private fun applyFilter() {
        val filterState = StatisticsFilterState(
            startMonth = currentFilterState.startMonth,
            endMonth = currentFilterState.endMonth,
            engineerName = binding.etEngineerName.text.toString().takeIf { it.isNotBlank() },
            sortBy = SortType.values()[binding.spinnerSort.selectedItemPosition]
        )

        onFilterAppliedListener?.invoke(filterState)
    }

    private fun resetFilter() {
        val emptyFilter = StatisticsFilterState()
        onFilterAppliedListener?.invoke(emptyFilter)
    }

    fun setOnFilterAppliedListener(listener: (StatisticsFilterState) -> Unit) {
        onFilterAppliedListener = listener
    }

    fun setCurrentFilter(filterState: StatisticsFilterState) {
        currentFilterState = filterState
    }
}
```

### 3.6 第六阶段：布局文件设计 (1-2天)

#### 3.6.1 主统计界面布局
```xml
<!-- fragment_statistics.xml -->
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="时效统计" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <androidx.appcompat.widget.SearchView
                android:id="@+id/searchView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:iconifiedByDefault="false"
                app:queryHint="搜索工程师名称" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnFilter"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                app:icon="@drawable/ic_filter" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="16dp" />

            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/ic_empty_data"
                    android:alpha="0.5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:text="暂无统计数据"
                    android:textColor="?android:attr/textColorSecondary" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
```

#### 3.6.2 统计列表项布局
```xml
<!-- item_statistics.xml -->
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 头部信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvEngineerName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="18sp"
                android:textStyle="bold"
                android:text="工程师姓名" />

            <TextView
                android:id="@+id/tvMonthly"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorSecondary"
                android:text="2024年03月" />

        </LinearLayout>

        <!-- 工单统计 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvOrderCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="25单"
                android:textColor="@color/primary" />

            <TextView
                android:id="@+id/tvReceiveCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="接单25单"
                android:textColor="?android:attr/textColorSecondary" />

        </LinearLayout>

        <!-- 时间指标 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="平均时间指标"
            android:textStyle="bold" />

        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:columnCount="2"
            android:rowCount="3">

            <!-- 接单时间 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:padding="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="接单时间"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/tvAvgReceiveTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="14小时11分钟"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progressReceive"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="2dp"
                    android:progress="30" />

            </LinearLayout>

            <!-- 备料时间 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:padding="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="备料时间"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/tvAvgPrepareTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="45分钟"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progressPrepare"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="2dp"
                    android:progress="10" />

            </LinearLayout>

            <!-- 路上时间 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:padding="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="路上时间"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/tvAvgOnRoadTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="6小时39分钟"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progressOnRoad"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="2dp"
                    android:progress="25" />

            </LinearLayout>

            <!-- 维修时间 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:padding="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="维修时间"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/tvAvgRepairTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="42小时43分钟"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progressRepair"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="2dp"
                    android:progress="60" />

            </LinearLayout>

            <!-- 确认时间 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:padding="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="确认时间"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/tvAvgConfirmTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4小时39分钟"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progressConfirm"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="2dp"
                    android:progress="15" />

            </LinearLayout>

        </GridLayout>

        <!-- 评价指标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="评价指标"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="专业："
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvProfessionalScore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.8分"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="服务："
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvServiceScore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.9分"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
```

## 4. 实施时间安排

### 4.1 开发阶段时间规划

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 第1阶段 | 数据模型层设计与实现 | 1-2天 | 后端开发 |
| 第2阶段 | 网络层API接口实现 | 2-3天 | 后端开发 |
| 第3阶段 | Repository和ViewModel实现 | 3-4天 | Android开发 |
| 第4阶段 | UI界面和适配器实现 | 4-6天 | Android开发 |
| 第5阶段 | 筛选搜索功能实现 | 2-3天 | Android开发 |
| 第6阶段 | 布局文件和样式设计 | 1-2天 | UI设计师 |
| 第7阶段 | 集成测试和优化 | 2-3天 | 全栈开发 |
| 第8阶段 | 用户验收测试 | 1-2天 | 测试团队 |

**总计：16-25天**

### 4.2 里程碑节点

- **第1周末**：完成数据模型和API接口
- **第2周末**：完成基础UI和数据展示
- **第3周末**：完成筛选搜索功能
- **第4周末**：完成集成测试和发布

## 5. 技术风险评估

### 5.1 高风险项

1. **API接口兼容性**
   - 风险：后端API可能与文档不完全一致
   - 应对：提前进行API联调测试
   - 缓解：预留接口适配时间

2. **大数据量性能**
   - 风险：统计数据量大可能影响性能
   - 应对：实现分页加载和数据缓存
   - 缓解：设置合理的分页大小

3. **复杂UI交互**
   - 风险：统计界面交互复杂度高
   - 应对：分阶段实现，先简单后复杂
   - 缓解：充分的用户测试

### 5.2 中风险项

1. **时间格式处理**
   - 风险：时间数据格式转换错误
   - 应对：统一时间处理工具类
   - 缓解：充分的单元测试

2. **筛选条件复杂性**
   - 风险：多条件筛选逻辑复杂
   - 应对：模块化筛选逻辑实现
   - 缓解：逐步增加筛选条件

### 5.3 低风险项

1. **UI样式适配**
   - 风险：不同设备适配问题
   - 应对：使用响应式布局
   - 缓解：多设备测试

## 6. 质量保证措施

### 6.1 代码质量

1. **代码规范**
   - 遵循Kotlin编码规范
   - 使用统一的命名约定
   - 添加完整的KDoc注释

2. **代码审查**
   - 所有代码必须经过同行审查
   - 使用静态代码分析工具
   - 定期进行代码重构

### 6.2 测试策略

1. **单元测试**
   - Repository层测试覆盖率>80%
   - ViewModel层测试覆盖率>80%
   - 工具类测试覆盖率>90%

2. **集成测试**
   - API接口集成测试
   - 端到端功能测试
   - 性能压力测试

3. **UI测试**
   - 关键用户流程自动化测试
   - 多设备兼容性测试
   - 用户体验测试

### 6.3 性能优化

1. **网络优化**
   - 请求缓存策略
   - 网络重试机制
   - 数据压缩传输

2. **内存优化**
   - 图片懒加载
   - 列表项回收复用
   - 内存泄漏检测

3. **用户体验优化**
   - 加载状态指示
   - 错误友好提示
   - 操作响应反馈

## 7. 部署和发布

### 7.1 发布准备

1. **版本管理**
   - 更新版本号
   - 编写发布说明
   - 准备回滚方案

2. **文档更新**
   - 用户使用手册
   - 开发者文档
   - API接口文档

### 7.2 发布策略

1. **灰度发布**
   - 先发布给内部测试用户
   - 逐步扩大用户范围
   - 监控关键指标

2. **监控告警**
   - 设置性能监控
   - 配置错误告警
   - 建立反馈渠道

## 8. 后续优化方向

### 8.1 功能增强

1. **数据可视化**
   - 添加图表展示
   - 趋势分析功能
   - 对比分析功能

2. **导出功能**
   - 数据导出Excel
   - 报表生成功能
   - 邮件分享功能

### 8.2 用户体验优化

1. **个性化设置**
   - 自定义筛选条件
   - 个人偏好保存
   - 快捷操作设置

2. **智能推荐**
   - 基于历史的智能筛选
   - 异常数据提醒
   - 优化建议推送

## 9. 总结

本实现计划基于现有Android项目的MVVM架构，充分复用了现有的网络层、数据层和UI组件，确保了代码的一致性和可维护性。通过分阶段实施、风险控制和质量保证措施，可以高效地完成工程师时效统计功能的集成。

关键成功因素：
1. 严格遵循现有架构模式
2. 充分的API联调测试
3. 分阶段渐进式开发
4. 完善的测试覆盖
5. 持续的性能优化

预期交付成果：
- 完整的时效统计功能模块
- 友好的用户交互界面
- 稳定的性能表现
- 完善的文档资料
