# Android端工程师时效统计接口文档

## 接口概述

本文档提供两个工程师时效统计相关的接口说明，用于Android端开发：
1. **月度统计接口** - 获取工程师月度时效汇总数据
2. **月度明细接口** - 获取工程师某月的详细工单时效数据

---

## 1. 工程师月度时效统计接口

### 基本信息
- **接口名称**: 工程师月度时效-月度统计
- **接口路径**: `/statistics/queryEngineerMonthTimelinessList`
- **请求方式**: POST
- **Content-Type**: application/json

### 请求参数

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认20 | 20 |
| startMonth | String | 否 | 开始月份(yyyy-MM格式) | "2024-01" |
| endMonth | String | 否 | 结束月份(yyyy-MM格式) | "2024-03" |
| name | String | 否 | 工程师名称(模糊查询) | "张工程师" |

#### 请求示例
```json
{
    "pageNum": 1,
    "pageSize": 20,
    "startMonth": "2024-01",
    "endMonth": "2024-03",
    "name": "罗修彬"
}
```

### 返回参数

#### 响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 50,
        "pageNum": 1,
        "pageSize": 20,
        "pages": 3,
        "list": [
            {
                "monthly": "2024-03",
                "engineerName": "罗修彬",
                "engineerId": 1874758610127433729,
                "orderNums": 25,
                "receiveNum": 25,
                "receiveTimeAvg": 851.61,
                "prepareTimeAvg": 45.61,
                "onRoadTimeAvg": 399.72,
                "repairTimeAvg": 2563.22,
                "confirmTimeAvg": 279.72,
                "professionalEvaluateAvg": 4.8,
                "serviceEvaluateAvg": 4.9,
                "receiveTime": 15329,
                "prepareTime": 821,
                "onRoadTime": 7195,
                "repairTime": 46138,
                "confirmTime": 5035,
                "professionalEvaluate": 120,
                "serviceEvaluate": 123
            }
        ]
    }
}
```

#### 返回字段说明

**分页信息**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |
| pages | Integer | 总页数 |

**统计数据 (list中的对象)**
| 字段名 | 类型 | 说明 | 单位 |
|--------|------|------|------|
| **基础信息** | | | |
| monthly | String | 月份 | yyyy-MM格式 |
| engineerName | String | 工程师名称 | - |
| engineerId | Long | 工程师ID | - |
| **工单统计** | | | |
| orderNums | Integer | 工单总数 | 个 |
| receiveNum | Integer | 接单数量总和 | 个 |
| **平均时间指标** | | | |
| receiveTimeAvg | Double | 平均接单时间 | 分钟 |
| prepareTimeAvg | Double | 平均备料时间 | 分钟 |
| onRoadTimeAvg | Double | 平均路上时间 | 分钟 |
| repairTimeAvg | Double | 平均维修时间 | 分钟 |
| confirmTimeAvg | Double | 平均确认时间 | 分钟 |
| **平均评价指标** | | | |
| professionalEvaluateAvg | Double | 平均专业评分 | 分 |
| serviceEvaluateAvg | Double | 平均服务态度评分 | 分 |
| **累计时间指标** | | | |
| receiveTime | Integer | 接单时间总和 | 分钟 |
| prepareTime | Integer | 备料时间总和 | 分钟 |
| onRoadTime | Integer | 路上时间总和 | 分钟 |
| repairTime | Integer | 维修时间总和 | 分钟 |
| confirmTime | Integer | 确认时间总和 | 分钟 |
| **累计评价指标** | | | |
| professionalEvaluate | Integer | 专业评分总和 | 分 |
| serviceEvaluate | Integer | 服务态度评分总和 | 分 |

---

## 2. 工程师月度时效明细接口

### 基本信息
- **接口名称**: 工程师月度时效-月度明细
- **接口路径**: `/statistics/queryEngineerMonthTimelinessDetailsList`
- **请求方式**: POST
- **Content-Type**: application/json

### 请求参数

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认20 | 20 |
| engineerId | Long | 是 | 工程师ID | 1874758610127433729 |
| monthly | String | 是 | 月份(yyyy-MM格式) | "2024-03" |
| code | String | 否 | 工单编号(模糊查询) | "WXGD240301" |
| name | String | 否 | 客户名称(模糊查询) | "测试客户" |

#### 请求示例
```json
{
    "pageNum": 1,
    "pageSize": 20,
    "engineerId": 1874758610127433729,
    "monthly": "2025-07",
    "code": "WXGD250701",
    "name": "客户名称"
}
```

### 返回参数

#### 响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 18,
        "pageNum": 1,
        "pageSize": 20,
        "pages": 1,
        "list": [
            {
                "id": 1001,
                "workOrderId": 1939926132543504385,
                "code": "WXGD250701000008",
                "productId": 12345,
                "engineerId": 1874758610127433729,
                "engineerName": "罗修彬",
                "monthly": "2025-07",
                "name": "测试客户",
                "receiveTime": 1166,
                "prepareTime": 0,
                "onRoadTime": 0,
                "repairTime": 21,
                "confirmTime": 1762,
                "receiveNum": 1,
                "professionalEvaluate": 5,
                "serviceEvaluate": 5,
                "evaluateTime": "2025-07-03T15:07:25",
                "createTime": "2025-07-01T13:56:47",
                "updateTime": "2025-07-03T15:07:26",
                "productInfo": "惠普/LaserJet Pro M404dn"
            }
        ]
    }
}
```

#### 返回字段说明

**分页信息**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |
| pages | Integer | 总页数 |

**明细数据 (list中的对象)**
| 字段名 | 类型 | 说明 | 单位 |
|--------|------|------|------|
| **基础信息** | | | |
| id | Long | 统计记录ID | - |
| workOrderId | Long | 工单ID | - |
| code | String | 工单编号 | - |
| productId | Long | 产品ID | - |
| engineerId | Long | 工程师ID | - |
| engineerName | String | 工程师名称 | - |
| monthly | String | 月份 | yyyy-MM格式 |
| name | String | 客户名称 | - |
| **时间指标** | | | |
| receiveTime | Integer | 接单时间 | 分钟 |
| prepareTime | Integer | 备料时间 | 分钟 |
| onRoadTime | Integer | 路上时间 | 分钟 |
| repairTime | Integer | 维修时间 | 分钟 |
| confirmTime | Integer | 确认时间 | 分钟 |
| receiveNum | Integer | 接单数量 | 个 |
| **评价指标** | | | |
| professionalEvaluate | Integer | 专业评分 | 分(1-5) |
| serviceEvaluate | Integer | 服务态度评分 | 分(1-5) |
| evaluateTime | String | 评价时间 | ISO格式 |
| **系统字段** | | | |
| createTime | String | 创建时间 | ISO格式 |
| updateTime | String | 更新时间 | ISO格式 |
| productInfo | String | 产品信息 | 品牌/型号 |

---

## Android端开发指导

### 1. 网络请求配置

#### Retrofit接口定义
```java
public interface StatisticsApi {
    
    @POST("statistics/queryEngineerMonthTimelinessList")
    Call<ApiResponse<PageData<MonthTimelinessVO>>> getMonthTimelinessList(
        @Body MonthTimelinessQuery query
    );
    
    @POST("statistics/queryEngineerMonthTimelinessDetailsList") 
    Call<ApiResponse<PageData<TimelinessDetailVO>>> getTimelinessDetailsList(
        @Body TimelinessDetailsQuery query
    );
}
```

#### 请求实体类
```java
// 月度统计查询参数
public class MonthTimelinessQuery extends PageQuery {
    private String startMonth;
    private String endMonth; 
    private String name;
    
    // getter/setter...
}

// 月度明细查询参数
public class TimelinessDetailsQuery extends PageQuery {
    @NonNull
    private Long engineerId;
    @NonNull
    private String monthly;
    private String code;
    private String name;
    
    // getter/setter...
}
```

### 2. 响应实体类

#### 月度统计VO
```java
public class MonthTimelinessVO {
    private String monthly;
    private String engineerName;
    private Long engineerId;
    private Integer orderNums;
    private Integer receiveNum;
    private Double receiveTimeAvg;
    private Double prepareTimeAvg;
    private Double onRoadTimeAvg;
    private Double repairTimeAvg;
    private Double confirmTimeAvg;
    private Double professionalEvaluateAvg;
    private Double serviceEvaluateAvg;
    private Integer receiveTime;
    private Integer prepareTime;
    private Integer onRoadTime;
    private Integer repairTime;
    private Integer confirmTime;
    private Integer professionalEvaluate;
    private Integer serviceEvaluate;
    
    // getter/setter...
}
```

#### 明细VO
```java
public class TimelinessDetailVO {
    private Long id;
    private Long workOrderId;
    private String code;
    private Long productId;
    private Long engineerId;
    private String engineerName;
    private String monthly;
    private String name;
    private Integer receiveTime;
    private Integer prepareTime;
    private Integer onRoadTime;
    private Integer repairTime;
    private Integer confirmTime;
    private Integer receiveNum;
    private Integer professionalEvaluate;
    private Integer serviceEvaluate;
    private String evaluateTime;
    private String createTime;
    private String updateTime;
    private String productInfo;
    
    // getter/setter...
}
```

### 3. 使用示例

#### 获取月度统计数据
```java
MonthTimelinessQuery query = new MonthTimelinessQuery();
query.setPageNum(1);
query.setPageSize(20);
query.setStartMonth("2024-01");
query.setEndMonth("2024-03");
query.setName("罗修彬");

statisticsApi.getMonthTimelinessList(query).enqueue(new Callback<ApiResponse<PageData<MonthTimelinessVO>>>() {
    @Override
    public void onResponse(Call<ApiResponse<PageData<MonthTimelinessVO>>> call, 
                          Response<ApiResponse<PageData<MonthTimelinessVO>>> response) {
        if (response.isSuccessful() && response.body().getCode() == 200) {
            PageData<MonthTimelinessVO> data = response.body().getData();
            List<MonthTimelinessVO> list = data.getList();
            // 处理数据...
        }
    }
    
    @Override
    public void onFailure(Call<ApiResponse<PageData<MonthTimelinessVO>>> call, Throwable t) {
        // 处理错误...
    }
});
```

#### 获取明细数据
```java
TimelinessDetailsQuery query = new TimelinessDetailsQuery();
query.setPageNum(1);
query.setPageSize(20);
query.setEngineerId(1874758610127433729L);
query.setMonthly("2025-07");

statisticsApi.getTimelinessDetailsList(query).enqueue(new Callback<ApiResponse<PageData<TimelinessDetailVO>>>() {
    @Override
    public void onResponse(Call<ApiResponse<PageData<TimelinessDetailVO>>> call, 
                          Response<ApiResponse<PageData<TimelinessDetailVO>>> response) {
        if (response.isSuccessful() && response.body().getCode() == 200) {
            PageData<TimelinessDetailVO> data = response.body().getData();
            List<TimelinessDetailVO> list = data.getList();
            // 处理数据...
        }
    }
    
    @Override
    public void onFailure(Call<ApiResponse<PageData<TimelinessDetailVO>>> call, Throwable t) {
        // 处理错误...
    }
});
```

### 4. 时间格式处理

#### 时间转换工具类
```java
public class TimeUtils {
    
    // 分钟转换为小时分钟格式
    public static String formatMinutes(int minutes) {
        if (minutes <= 0) return "0分钟";
        
        int hours = minutes / 60;
        int mins = minutes % 60;
        
        if (hours > 0) {
            return hours + "小时" + (mins > 0 ? mins + "分钟" : "");
        } else {
            return mins + "分钟";
        }
    }
    
    // ISO时间格式转换
    public static String formatISOTime(String isoTime) {
        try {
            SimpleDateFormat input = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
            SimpleDateFormat output = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
            Date date = input.parse(isoTime);
            return output.format(date);
        } catch (ParseException e) {
            return isoTime;
        }
    }
}
```

### 5. 错误处理

#### 常见错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理数据 |
| 400 | 参数错误 | 检查必填参数 |
| 401 | 未授权 | 重新登录 |
| 500 | 服务器错误 | 提示用户稍后重试 |

#### 错误处理示例
```java
if (response.body().getCode() != 200) {
    String message = response.body().getMessage();
    // 显示错误信息
    Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    return;
}
```

---

## 注意事项

1. **必填参数验证**: 明细接口的engineerId和monthly参数必须传递
2. **时间格式**: 月份参数必须使用yyyy-MM格式
3. **分页处理**: 建议使用RecyclerView配合分页加载
4. **时间显示**: 建议将分钟数转换为更友好的时间格式显示
5. **异常值处理**: 对于过大的时间值(如维修时间超过几天)建议特殊显示
6. **网络超时**: 建议设置合适的网络超时时间
7. **数据缓存**: 可考虑对统计数据进行适当缓存以提升用户体验
