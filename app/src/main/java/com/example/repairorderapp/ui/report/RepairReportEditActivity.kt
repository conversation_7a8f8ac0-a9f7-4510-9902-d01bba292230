package com.example.repairorderapp.ui.report

import android.Manifest
import android.app.Activity
import android.app.ProgressDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.text.TextUtils
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bumptech.glide.Glide
import com.example.repairorderapp.BuildConfig
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.data.api.UploadImageResponse
import com.zhihu.matisse.Matisse
import com.zhihu.matisse.MimeType
import com.zhihu.matisse.engine.impl.GlideEngine
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File
import android.widget.Spinner
import android.widget.ArrayAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import android.app.AlertDialog
import com.google.gson.Gson
import android.view.View
import android.view.ViewGroup
import android.view.LayoutInflater
import android.text.TextWatcher
import android.text.Editable
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.net.Uri
import com.example.repairorderapp.util.ToastUtil
import java.util.UUID
import com.example.repairorderapp.ui.orders.PartAdapter
import com.example.repairorderapp.ui.orders.PartItem
import com.example.repairorderapp.model.Option
import com.example.repairorderapp.util.setDebounceClickListener
import android.widget.ImageButton
import android.provider.MediaStore
import androidx.core.content.FileProvider
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import android.graphics.Color
import android.os.Environment
import android.widget.FrameLayout
import android.view.Gravity
import com.github.chrisbanes.photoview.PhotoView
import android.app.Dialog
import com.example.repairorderapp.ui.report.ClearableSpinnerAdapter
import com.example.repairorderapp.ui.common.ImageViewerDialog
import android.graphics.BitmapFactory
import android.graphics.Bitmap
import java.io.ByteArrayOutputStream
import com.example.repairorderapp.RepairOrderApp
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.COSXMLUploadTask
import com.tencent.cos.xml.transfer.TransferConfig
import com.tencent.cos.xml.transfer.TransferManager
import com.tencent.cos.xml.transfer.TransferState
import com.tencent.cos.xml.transfer.TransferStateListener
import java.io.InputStream
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.ConcurrentHashMap
import java.io.FileOutputStream
import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.view.TouchDelegate
import android.graphics.Rect

data class Option(val value: String, val label: String)
data class CosObject(val url: String)

class RepairReportEditActivity : AppCompatActivity() {
    private lateinit var workOrderApi: WorkOrderApi
    private var orderId: String? = null
    private var customerId: String? = null
    private var deviceGroupId: String? = null
    private lateinit var etContent: EditText
    private lateinit var etResult: EditText
    private lateinit var btnSubmit: Button
    
    // 只保留客户自配配件明细和工程师领料配件明细两组
    private lateinit var rvCustomerParts: RecyclerView
    private lateinit var rvEngineerParts: RecyclerView
    private lateinit var customerPartAdapter: PartAdapter
    private lateinit var engineerPartAdapter: PartAdapter
    private val customerParts = mutableListOf<PartItem>()
    private val engineerParts = mutableListOf<PartItem>()

    // 分类选项
    private lateinit var spinnerPhenomenon: Spinner
    private lateinit var spinnerReason: Spinner
    private lateinit var spinnerHandleType: Spinner
    private lateinit var spinnerBreakdown: Spinner
    private var phenomenonOptions: List<Option> = listOf()
    private var reasonOptions: List<Option> = listOf()
    private var handleTypeOptions: List<Option> = listOf()
    private var breakdownOptions: List<Option> = listOf()

    // 计数器、下次注意事项等
    private lateinit var etAnnouncements: EditText
    private lateinit var etBlackWhiteCount: EditText
    private lateinit var etColorCount: EditText
    private lateinit var etFiveColorCount: EditText
    private lateinit var etBlackWhiteExclude: EditText
    private lateinit var etColorExclude: EditText
    private lateinit var etFiveColorExclude: EditText

    // 只保留故障描述图片与解决措施图片
    private val excDescImageUris = mutableListOf<Uri>()
    private val resolveDescImageUris = mutableListOf<Uri>()
    private lateinit var excDescImageContainer: LinearLayout
    private lateinit var resolveDescImageContainer: LinearLayout
    private val REQUEST_CODE_GALLERY_EXC_DESC = 1001
    private val REQUEST_CODE_GALLERY_RESOLVE_DESC = 1002
    private val REQUEST_CODE_CAMERA_EXC_DESC = 1003
    private val REQUEST_CODE_CAMERA_RESOLVE_DESC = 1004
    private val REQUEST_CAMERA_PERMISSION = 1005
    private val REQUEST_STORAGE_PERMISSION = 1006
    private var pendingCameraRequestCode = 0
    private var pendingGalleryRequestCode = 0
    private var excDescImageUrls: List<String> = emptyList()
    private var resolveDescImageUrls: List<String> = emptyList()
    private var currentPhotoPath: String = ""

    private val REQUEST_CODE_SELECT_CUSTOMER_PART = 2001
    private val REQUEST_CODE_SELECT_ENGINEER_PART = 2002

    // 初始化图片添加按钮
    private lateinit var btnAddExcDescImage: ImageButton
    private lateinit var btnAddResolveDescImage: ImageButton

    private var transferManager: TransferManager? = null
    private val transferManagerInitHandler = Handler(Looper.getMainLooper())


    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_repair_report_edit)
        orderId = intent.getStringExtra("orderId")
        customerId = intent.getStringExtra("customerId")
        deviceGroupId = intent.getStringExtra("deviceGroupId")
        workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
        etContent = findViewById(R.id.et_content)
        etResult = findViewById(R.id.et_result)
        btnSubmit = findViewById(R.id.btn_submit_report)
        // 计数器、下次注意事项
        etAnnouncements = findViewById(R.id.et_announcements)
        etBlackWhiteCount = findViewById(R.id.et_black_white_count)
        etColorCount = findViewById(R.id.et_color_count)
        etFiveColorCount = findViewById(R.id.et_five_color_count)
        etBlackWhiteExclude = findViewById(R.id.et_black_white_exclude)
        etColorExclude = findViewById(R.id.et_color_exclude)
        etFiveColorExclude = findViewById(R.id.et_five_color_exclude)
        // 加载草稿
        loadDraftIfExists()

        // 初始化配件明细RecyclerView
        initPartsRecyclerViews()
        
        // 分类Spinner初始化
        spinnerPhenomenon = findViewById(R.id.spinner_phenomenon)
        spinnerReason = findViewById(R.id.spinner_reason)
        spinnerHandleType = findViewById(R.id.spinner_handle_type)
        spinnerBreakdown = findViewById(R.id.spinner_breakdown)
        
        // 初始化故障描述图片与解决措施图片
        excDescImageContainer = findViewById(R.id.layout_exc_desc_images)
        resolveDescImageContainer = findViewById(R.id.layout_resolve_desc_images)
        // 页面初始化时直接显示添加按钮
        showSelectedImages(excDescImageContainer, excDescImageUris)
        showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
        
        // 设置下拉框样式
        setUpSpinnerStyles()

        // 初始化按钮监听 - 使用防抖
        findViewById<Button>(R.id.btn_save_draft).setDebounceClickListener(1500) { saveDraft() }
        findViewById<Button>(R.id.btn_submit_report).setDebounceClickListener(1500) { confirmSubmit() }
        // 获取分类选项
        fetchOptions()

        // 替换添加配件按钮逻辑为跳转选择页面 - 使用防抖
        findViewById<Button>(R.id.btn_add_customer_part).setDebounceClickListener {
            val intent = Intent(this, com.example.repairorderapp.ui.orders.PartSelectActivity::class.java)
            intent.putExtra("type", "customer")
            intent.putExtra("customerId", customerId ?: "")
            intent.putExtra("deviceGroupId", deviceGroupId ?: "")
            intent.putExtra("existParts", ArrayList(customerParts))
            startActivityForResult(intent, REQUEST_CODE_SELECT_CUSTOMER_PART)
        }
        findViewById<Button>(R.id.btn_add_engineer_part).setDebounceClickListener {
            val intent = Intent(this, com.example.repairorderapp.ui.orders.PartSelectActivity::class.java)
            intent.putExtra("type", "engineer")
            intent.putExtra("deviceGroupId", deviceGroupId ?: "")
            intent.putExtra("existParts", ArrayList(engineerParts))
            startActivityForResult(intent, REQUEST_CODE_SELECT_ENGINEER_PART)
        }

        // 初始化 TransferManager
        initTransferManagerWithRetry()

        // 点击空白区域收起键盘并失去焦点
        findViewById<View>(R.id.root_layout).setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 获取所有 EditText
                val editTexts = listOf(
                    etContent, etResult, etAnnouncements,
                    etBlackWhiteCount, etColorCount, etFiveColorCount,
                    etBlackWhiteExclude, etColorExclude, etFiveColorExclude
                )
                // 判断点击点是否在任一 EditText 区域内
                var isInEditText = false
                for (et in editTexts) {
                    val location = IntArray(2)
                    et.getLocationOnScreen(location)
                    val left = location[0]
                    val top = location[1]
                    val right = left + et.width
                    val bottom = top + et.height
                    val x = event.rawX.toInt()
                    val y = event.rawY.toInt()
                    if (x in left..right && y in top..bottom) {
                        isInEditText = true
                        break
                    }
                }
                if (!isInEditText) {
                    // 只有点击非输入框区域才收起键盘并清除焦点
                    val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                    currentFocus?.let { imm.hideSoftInputFromWindow(it.windowToken, 0) }
                    // 清除所有 EditText 焦点
                    editTexts.forEach { it.clearFocus() }
                    v.requestFocus() // 让根布局获得焦点
                }
            }
            false
        }
        

    }
    
    override fun onDestroy() {
        super.onDestroy()
    }

    private fun loadDraftIfExists() {
        try {
            // 显示加载对话框
            val loadingDialog = ProgressDialog.show(this, null, "正在加载草稿...", true, false)
            
            // 从服务器加载草稿
            loadDraftFromServer { serverDraft ->
                try {
                    android.util.Log.d("RepairReport", "服务器草稿加载完成，结果: ${serverDraft != null}")
                    
                    // 将服务器草稿数据应用到UI
                    if (serverDraft != null) {
                        applyDraftToUI(serverDraft)
                        Toast.makeText(this, "草稿已恢复", Toast.LENGTH_SHORT).show()
                    } else {
                        android.util.Log.d("RepairReport", "没有可用的草稿数据")
                    }
                    
                    loadingDialog.dismiss()
                } catch (e: Exception) {
                    android.util.Log.e("RepairReport", "服务器草稿处理异常", e)
                    loadingDialog.dismiss()
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "草稿恢复整体异常", e)
            Toast.makeText(this, "草稿恢复异常: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 从服务器加载草稿
     */
    private fun loadDraftFromServer(callback: (Map<String, Any?>?) -> Unit) {
        if (orderId.isNullOrEmpty()) {
            callback(null)
            return
        }
        
        workOrderApi.getPopReport(orderId!!).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                try {
                        if (response.isSuccessful && response.body()?.code == 200) {
                        val data = response.body()?.data
                        if (data != null) {
                            android.util.Log.d("RepairReport", "服务器草稿获取成功: $data")
                            
                            // 将服务器数据转换为草稿格式
                            val draft = convertServerDataToDraft(data)
                            callback(draft)
                        } else {
                            android.util.Log.d("RepairReport", "服务器草稿为空")
                            callback(null)
                        }
                    } else {
                        android.util.Log.e("RepairReport", "服务器草稿获取失败: ${response.body()?.msg}")
                        callback(null)
                                    }
                                } catch (e: Exception) {
                    android.util.Log.e("RepairReport", "处理服务器草稿异常", e)
                    callback(null)
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                android.util.Log.e("RepairReport", "获取服务器草稿网络错误", t)
                callback(null)
            }
        })
    }
    

    
    /**
     * 将服务器数据转换为本地草稿格式
     */
    private fun convertServerDataToDraft(serverData: Any): Map<String, Any?>? {
        try {
            val draft = HashMap<String, Any?>()
            
            // 将服务器数据转为Map
            val dataMap = when (serverData) {
                is Map<*, *> -> serverData as Map<*, *>
                else -> {
                    val json = Gson().toJson(serverData)
                    Gson().fromJson<Map<String, Any?>>(
                        json,
                        object : com.google.gson.reflect.TypeToken<Map<String, Any?>>() {}.type
                    )
                }
            }
            
            android.util.Log.d("RepairReport", "开始转换服务器草稿: ${Gson().toJson(dataMap)}")
            
            // 填充基本字段
            draft["orderId"] = orderId
            draft["customerId"] = customerId
            draft["deviceGroupId"] = deviceGroupId
            draft["excDesc"] = dataMap["excDesc"] as? String ?: ""
            draft["resolveDesc"] = dataMap["resolveDesc"] as? String ?: ""
            draft["announcements"] = dataMap["announcements"] as? String ?: ""
            
            // 计数器相关字段 - 处理浮点数格式
            // 处理blackWhiteCount字段 - 可能是浮点数或字符串
            when (val blackWhiteCount = dataMap["blackWhiteCount"]) {
                is Number -> draft["blackWhiteCount"] = blackWhiteCount.toLong().toString()
                is String -> draft["blackWhiteCount"] = blackWhiteCount
                else -> draft["blackWhiteCount"] = ""
            }
            
            // 处理colorCount字段 - 可能是浮点数或字符串
            when (val colorCount = dataMap["colorCount"]) {
                is Number -> draft["colorCount"] = colorCount.toLong().toString()
                is String -> draft["colorCount"] = colorCount
                else -> draft["colorCount"] = ""
            }
            
            // 处理fiveColorCount字段 - 可能是浮点数或字符串，且名称可能有变化
            when (val fiveColorCount = dataMap["fiveColourCount"] ?: dataMap["fiveColorCount"]) {
                is Number -> draft["fiveColorCount"] = fiveColorCount.toLong().toString()
                is String -> draft["fiveColorCount"] = fiveColorCount
                else -> draft["fiveColorCount"] = ""
            }
            
            // 处理exclude字段
            when (val blackWhiteExclude = dataMap["blackWhiteExclude"]) {
                is Number -> draft["blackWhiteExclude"] = blackWhiteExclude.toLong().toString()
                is String -> draft["blackWhiteExclude"] = blackWhiteExclude
                else -> draft["blackWhiteExclude"] = ""
            }
            
            when (val colorExclude = dataMap["colorExclude"]) {
                is Number -> draft["colorExclude"] = colorExclude.toLong().toString()
                is String -> draft["colorExclude"] = colorExclude
                else -> draft["colorExclude"] = ""
            }
            
            when (val fiveColorExclude = dataMap["fiveColorExclude"]) {
                is Number -> draft["fiveColorExclude"] = fiveColorExclude.toLong().toString()
                is String -> draft["fiveColorExclude"] = fiveColorExclude
                else -> draft["fiveColorExclude"] = ""
            }
            
            android.util.Log.d("RepairReport", "计数器字段处理: blackWhiteCount=${draft["blackWhiteCount"]}, colorCount=${draft["colorCount"]}, fiveColorCount=${draft["fiveColorCount"]}")
            
            // 处理分类选项索引 - 先存储服务器返回的完整对象
            val excType = dataMap["excType"] as? Map<*, *>
            val reasonType = dataMap["reasonType"] as? Map<*, *>
            val resolveType = dataMap["resolveType"] as? Map<*, *>
            val excUnit = dataMap["excUnit"] as? Map<*, *>
            
            draft["excTypeObj"] = excType
            draft["reasonTypeObj"] = reasonType
            draft["resolveTypeObj"] = resolveType
            draft["excUnitObj"] = excUnit
            
            // 临时初始化索引为0（稍后会在applyDraftToUI方法中根据value匹配更新）
            draft["phenomenonIndex"] = 0
            draft["reasonIndex"] = 0
            draft["handleTypeIndex"] = 0
            draft["breakdownIndex"] = 0
            
            // 保存图片信息 - 提取URL到单独的列表中
            val excDescPics = dataMap["excDescPics"] as? List<*> ?: emptyList<Any>()
            val resolveDescPics = dataMap["resolveDescPics"] as? List<*> ?: emptyList<Any>()
            
            // 提取图片URL到单独的列表
            val excDescUrls = excDescPics.mapNotNull { pic ->
                if (pic is Map<*, *>) {
                    pic["url"] as? String
                } else {
                    null
                }
            }
            
            val resolveDescUrls = resolveDescPics.mapNotNull { pic ->
                if (pic is Map<*, *>) {
                    pic["url"] as? String
                } else {
                    null
                }
            }
            
            // 保存图片URL列表
            draft["excDescImageUrls"] = excDescUrls
            draft["resolveDescImageUrls"] = resolveDescUrls
            
            // 创建空的本地URI列表 - 将在applyDraftToUI中转换
            draft["excDescImageUris"] = emptyList<String>()
            draft["resolveDescImageUris"] = emptyList<String>()
            
            android.util.Log.d("RepairReport", "提取的图片URLs: 故障描述=${excDescUrls.size}张, 解决措施=${resolveDescUrls.size}张")
            
            // 记录服务器草稿的时间戳
            draft["timestamp"] = System.currentTimeMillis()
            
            // 处理零件更换列表
            val replaceInfoList = dataMap["replaceInfoList"] as? List<*> ?: emptyList<Any>()
            val customerParts = mutableListOf<Map<String, Any?>>()
            val engineerParts = mutableListOf<Map<String, Any?>>()
            
            // 处理零件数据
            replaceInfoList.forEach { item ->
                if (item is Map<*, *>) {
                    val itemStore = item["itemStore"] as? Map<*, *>
                    if (itemStore != null) {
                        val userType = itemStore["userType"] as? String
                        val part = HashMap<String, Any?>()
                        
                        part["id"] = item["itemStoreId"]
                        
                        // 明确记录字段来源，避免混淆
                        // itemStore["num"]是零件总库存数量
                        // item["num"]是已使用/更换的数量
                        val stockNum = (itemStore["num"] as? Number)?.toInt() ?: 0
                        val usedNum = (item["num"] as? Number)?.toInt() ?: 0
                        
                        // 正确设置库存数量和使用数量
                        part["num"] = stockNum  // 总库存，用于限制最大更换数量
                        part["changeNum"] = usedNum  // 更换数量，提交时使用
                        
                        part["location"] = (item["location"] as? List<*>)?.firstOrNull()?.toString() ?: ""
                        part["itemName"] = itemStore["itemName"]
                        part["skuInfo"] = itemStore["skuInfo"]
                        part["oemNumber"] = itemStore["oemNumber"]
                        part["saleUnitPrice"] = itemStore["saleUnitPrice"]
                        part["articleCode"] = itemStore["articleCode"] // 添加articleCode用于分组计算
                        part["batchCode"] = itemStore["batchCode"] // 添加batchCode用于分组计算
                        
                        if (userType == "CUSTOMER") {
                            customerParts.add(part)
                        } else if (userType == "ENGINEER") {
                            engineerParts.add(part)
                        }
                    }
                }
            }
            
            draft["customerParts"] = customerParts
            draft["engineerParts"] = engineerParts
            
            android.util.Log.d("RepairReport", "解析零件信息: 客户零件=${customerParts.size}个, 工程师零件=${engineerParts.size}个")
            
            // 记录草稿来源
            draft["source"] = "server"
            
            return draft
            } catch (e: Exception) {
            android.util.Log.e("RepairReport", "转换服务器数据失败", e)
            return null
        }
    }
    

    
    /**
     * 将草稿数据应用到UI
     */
    private fun applyDraftToUI(draft: Map<String, Any?>) {
        // 记录草稿加载开始
        android.util.Log.d("RepairReport", "开始加载草稿, orderId=$orderId")
        
        try {
            // 1. 恢复简单文本字段
            try {
                etContent.setText(draft["excDesc"] as? String ?: "")
                etResult.setText(draft["resolveDesc"] as? String ?: "")
                etAnnouncements.setText(draft["announcements"] as? String ?: "")
                
                // 处理计数器字段 - 确保正确处理字符串格式的数值
                // 黑白计数器
                val blackWhiteCount = draft["blackWhiteCount"]
                when (blackWhiteCount) {
                    is String -> etBlackWhiteCount.setText(blackWhiteCount)
                    is Number -> etBlackWhiteCount.setText(blackWhiteCount.toString())
                    else -> etBlackWhiteCount.setText("")
                }
                
                // 彩色计数器
                val colorCount = draft["colorCount"]
                when (colorCount) {
                    is String -> etColorCount.setText(colorCount)
                    is Number -> etColorCount.setText(colorCount.toString())
                    else -> etColorCount.setText("")
                }
                
                // 五色计数器
                val fiveColorCount = draft["fiveColorCount"]
                when (fiveColorCount) {
                    is String -> etFiveColorCount.setText(fiveColorCount)
                    is Number -> etFiveColorCount.setText(fiveColorCount.toString())
                    else -> etFiveColorCount.setText("")
                }
                
                // 排除字段
                val blackWhiteExclude = draft["blackWhiteExclude"]
                when (blackWhiteExclude) {
                    is String -> etBlackWhiteExclude.setText(blackWhiteExclude)
                    is Number -> etBlackWhiteExclude.setText(blackWhiteExclude.toString())
                    else -> etBlackWhiteExclude.setText("")
                }
                
                val colorExclude = draft["colorExclude"]
                when (colorExclude) {
                    is String -> etColorExclude.setText(colorExclude)
                    is Number -> etColorExclude.setText(colorExclude.toString())
                    else -> etColorExclude.setText("")
                }
                
                val fiveColorExclude = draft["fiveColorExclude"]
                when (fiveColorExclude) {
                    is String -> etFiveColorExclude.setText(fiveColorExclude)
                    is Number -> etFiveColorExclude.setText(fiveColorExclude.toString())
                    else -> etFiveColorExclude.setText("")
                }
                
                android.util.Log.d("RepairReport", "计数器字段恢复: blackWhiteCount=$blackWhiteCount, colorCount=$colorCount, fiveColorCount=$fiveColorCount")
                android.util.Log.d("RepairReport", "文本字段恢复成功")
            } catch (e: Exception) {
                android.util.Log.e("RepairReport", "恢复文本字段异常: ${e.message}", e)
            }
            
            // 2. 处理下拉选项
            try {
                // 检查是否有来自服务器的分类选项对象
                val excTypeObj = draft["excTypeObj"] as? Map<*, *>
                val reasonTypeObj = draft["reasonTypeObj"] as? Map<*, *>
                val resolveTypeObj = draft["resolveTypeObj"] as? Map<*, *>
                val excUnitObj = draft["excUnitObj"] as? Map<*, *>
                
                // 如果有服务器返回的选项对象，优先使用它们
                if (excTypeObj != null) {
                    val value = excTypeObj["value"] as? String
                    val label = excTypeObj["label"] as? String
                    if (!value.isNullOrEmpty() && !label.isNullOrEmpty()) {
                        // 服务器返回的分类选项
                        val serverOption = Option(value, label)
                        android.util.Log.d("RepairReport", "服务器返回的现象分类: $serverOption")
                        setSpinnerWithServerOption("6000", spinnerPhenomenon, serverOption, "现象分类")
                    }
                } else {
                    // 否则尝试使用索引
                    val phenomenonIndex = extractIntValue(draft["phenomenonIndex"])
                    if (phenomenonIndex > 0) {
                        setUpSpinnerWithDraft("6000", spinnerPhenomenon, phenomenonOptions, phenomenonIndex, "现象分类")
                    }
                }
                
                if (reasonTypeObj != null) {
                    val value = reasonTypeObj["value"] as? String
                    val label = reasonTypeObj["label"] as? String
                    if (!value.isNullOrEmpty() && !label.isNullOrEmpty()) {
                        val serverOption = Option(value, label)
                        android.util.Log.d("RepairReport", "服务器返回的原因分类: $serverOption")
                        setSpinnerWithServerOption("7000", spinnerReason, serverOption, "原因分类")
                    }
                } else {
                    val reasonIndex = extractIntValue(draft["reasonIndex"])
                    if (reasonIndex > 0) {
                        setUpSpinnerWithDraft("7000", spinnerReason, reasonOptions, reasonIndex, "原因分类")
                    }
                }
                
                if (resolveTypeObj != null) {
                    val value = resolveTypeObj["value"] as? String
                    val label = resolveTypeObj["label"] as? String
                    if (!value.isNullOrEmpty() && !label.isNullOrEmpty()) {
                        val serverOption = Option(value, label)
                        android.util.Log.d("RepairReport", "服务器返回的处理类型: $serverOption")
                        setSpinnerWithServerOption("8000", spinnerHandleType, serverOption, "处理类型")
                    }
                } else {
                    val handleTypeIndex = extractIntValue(draft["handleTypeIndex"])
                    if (handleTypeIndex > 0) {
                        setUpSpinnerWithDraft("8000", spinnerHandleType, handleTypeOptions, handleTypeIndex, "处理类型")
                    }
                }
                
                if (excUnitObj != null) {
                    val value = excUnitObj["value"] as? String
                    val label = excUnitObj["label"] as? String
                    if (!value.isNullOrEmpty() && !label.isNullOrEmpty()) {
                        val serverOption = Option(value, label)
                        android.util.Log.d("RepairReport", "服务器返回的故障组件: $serverOption")
                        setSpinnerWithServerOption("9000", spinnerBreakdown, serverOption, "故障组件")
                    }
                } else {
                    val breakdownIndex = extractIntValue(draft["breakdownIndex"])
                    if (breakdownIndex > 0) {
                        setUpSpinnerWithDraft("9000", spinnerBreakdown, breakdownOptions, breakdownIndex, "故障组件")
                    }
                }
                
                android.util.Log.d("RepairReport", "分类选项处理完成")
            } catch (e: Exception) {
                android.util.Log.e("RepairReport", "处理分类选项异常: ${e.message}", e)
            }
            
            // 3. 恢复图片数据
            val mainHandler = android.os.Handler(android.os.Looper.getMainLooper())
            mainHandler.postDelayed({
                try {
                    // 3.1 处理服务器返回的图片URL
                    val excDescImageUrls = draft["excDescImageUrls"] as? List<String>
                    val resolveDescImageUrls = draft["resolveDescImageUrls"] as? List<String>
                    
                    // 如果有服务器返回的图片URL，保存到成员变量
                    if (!excDescImageUrls.isNullOrEmpty()) {
                        this.excDescImageUrls = excDescImageUrls
                        android.util.Log.d("RepairReport", "保存服务器故障描述图片URLs: ${excDescImageUrls.size}张")
                    }
                    
                    if (!resolveDescImageUrls.isNullOrEmpty()) {
                        this.resolveDescImageUrls = resolveDescImageUrls
                        android.util.Log.d("RepairReport", "保存服务器解决措施图片URLs: ${resolveDescImageUrls.size}张")
                    }
                    
                    // 如果有服务器图片URL，使用统一的重建方法
                    if (!this.excDescImageUrls.isEmpty() || !this.resolveDescImageUrls.isEmpty()) {
                        // 清理重复URL（如果有的话）
                        deduplicateImageUrls()
                        
                        // 重建虚拟URI列表，确保与URL列表一致
                        rebuildVirtualUriLists()
                        
                        // 显示图片
                        if (this::excDescImageContainer.isInitialized && excDescImageUris.isNotEmpty()) {
                            showSelectedImages(excDescImageContainer, excDescImageUris)
                            android.util.Log.d("RepairReport", "显示故障描述图片: ${excDescImageUris.size}张")
                        }
                        
                        if (this::resolveDescImageContainer.isInitialized && resolveDescImageUris.isNotEmpty()) {
                            showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
                            android.util.Log.d("RepairReport", "显示解决措施图片: ${resolveDescImageUris.size}张")
                        }
                    }
                    
                    // 3.2 处理本地URI
                    val excDescImageList = draft["excDescImageUris"] as? List<*>
                    if (excDescImageList != null && excDescImageList.isNotEmpty() && excDescImageUrls.isNullOrEmpty()) {
                        android.util.Log.d("RepairReport", "开始恢复本地故障描述图片, 数量: ${excDescImageList.size}")
                        excDescImageUris.clear()
                        
                        val validUris = excDescImageList.mapNotNull { item ->
                            try {
                                val uriStr = item?.toString() ?: return@mapNotNull null
                                Uri.parse(uriStr)
                            } catch (e: Exception) {
                                android.util.Log.e("RepairReport", "解析故障描述图片URI异常: $item", e)
                                null
                            }
                        }
                        
                        excDescImageUris.addAll(validUris)
                        android.util.Log.d("RepairReport", "恢复本地故障描述图片成功, 有效数量: ${validUris.size}")
                        
                        if (this::excDescImageContainer.isInitialized && excDescImageUris.isNotEmpty()) {
                            showSelectedImages(excDescImageContainer, excDescImageUris)
                        }
                }
                
                    val resolveImageList = draft["resolveDescImageUris"] as? List<*>
                    if (resolveImageList != null && resolveImageList.isNotEmpty() && resolveDescImageUrls.isNullOrEmpty()) {
                        android.util.Log.d("RepairReport", "开始恢复本地解决措施图片, 数量: ${resolveImageList.size}")
                        resolveDescImageUris.clear()
                        
                        val validUris = resolveImageList.mapNotNull { item ->
                            try {
                                val uriStr = item?.toString() ?: return@mapNotNull null
                                Uri.parse(uriStr)
                            } catch (e: Exception) {
                                android.util.Log.e("RepairReport", "解析解决措施图片URI异常: $item", e)
                                null
                            }
                        }
                        
                        resolveDescImageUris.addAll(validUris)
                        android.util.Log.d("RepairReport", "恢复本地解决措施图片成功, 有效数量: ${validUris.size}")
                        
                        if (this::resolveDescImageContainer.isInitialized && resolveDescImageUris.isNotEmpty()) {
                            showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("RepairReport", "恢复图片数据异常", e)
                }
            }, 800)
            
            // 4. 恢复配件列表数据
            mainHandler.postDelayed({
                try {
                    // 4.1 恢复客户配件列表
                    val customerPartsList = draft["customerParts"] as? List<*>
                    if (customerPartsList != null) {
                        android.util.Log.d("RepairReport", "开始恢复客户配件列表, 数量: ${customerPartsList.size}")
                        val jsonStr = Gson().toJson(customerPartsList)
                        val type = object : com.google.gson.reflect.TypeToken<List<PartItem>>() {}.type
                        val parts = try {
                            Gson().fromJson<List<PartItem>>(jsonStr, type)
                        } catch (e: Exception) {
                            android.util.Log.e("RepairReport", "反序列化客户配件列表异常: $jsonStr", e)
                            null
                        }
                        if (parts != null && parts.isNotEmpty()) {
                            // 修正location字段为String
                            parts.forEach { it.location = it.location?.toString() ?: "" }
                            customerParts.clear()
                            customerParts.addAll(parts)
                            android.util.Log.d("RepairReport", "客户配件列表恢复成功, 数量: ${parts.size}")
                            if (this::customerPartAdapter.isInitialized) {
                                customerPartAdapter.notifyDataSetChanged()
                            } else {
                                android.util.Log.w("RepairReport", "客户配件适配器未初始化")
                            }
                        }
                }
                
                    // 4.2 恢复工程师配件列表
                    val engineerPartsList = draft["engineerParts"] as? List<*>
                    if (engineerPartsList != null) {
                        android.util.Log.d("RepairReport", "开始恢复工程师配件列表, 数量: ${engineerPartsList.size}")
                        val jsonStr = Gson().toJson(engineerPartsList)
                        val type = object : com.google.gson.reflect.TypeToken<List<PartItem>>() {}.type
                        val parts = try {
                            Gson().fromJson<List<PartItem>>(jsonStr, type)
                        } catch (e: Exception) {
                            android.util.Log.e("RepairReport", "反序列化工程师配件列表异常: $jsonStr", e)
                            null
                        }
                        if (parts != null && parts.isNotEmpty()) {
                            // 修正location字段为String
                            parts.forEach { it.location = it.location?.toString() ?: "" }
                            engineerParts.clear()
                            engineerParts.addAll(parts)
                            android.util.Log.d("RepairReport", "工程师配件列表恢复成功, 数量: ${parts.size}")
                            if (this::engineerPartAdapter.isInitialized) {
                                engineerPartAdapter.notifyDataSetChanged()
                            } else {
                                android.util.Log.w("RepairReport", "工程师配件适配器未初始化")
                            }
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("RepairReport", "恢复配件列表异常", e)
                }
            }, 1000)
            
            // 显示恢复成功提示
            Toast.makeText(this, "草稿已恢复", Toast.LENGTH_SHORT).show()
            android.util.Log.d("RepairReport", "草稿恢复流程完成")
            
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "草稿恢复整体异常", e)
            Toast.makeText(this, "草稿恢复异常: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 从各种类型中提取整数值
     */
    private fun extractIntValue(value: Any?): Int {
        return try {
            when (value) {
                is Double -> value.toInt()
                is Int -> value
                is Number -> value.toInt()
                is String -> value.toIntOrNull() ?: 0
                else -> 0
            }
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "提取整数值异常", e)
            0
        }
    }
    
    /**
     * 使用服务器返回的选项设置Spinner
     */
    private fun setSpinnerWithServerOption(
        dictCode: String,
        spinner: Spinner,
        serverOption: Option,
        fieldName: String
    ) {
        try {
            // 加载字典选项
            workOrderApi.getDictOptions(dictCode).enqueue(object : Callback<ApiResponse<List<Option>>> {
                override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                    if (response.isSuccessful && response.body()?.code == 200) {
                        val options = response.body()?.data ?: emptyList()
                        val fullOptions = listOf(Option("", "请选择")) + options
                        
                        // 根据字典代码更新对应的选项列表
                        when (dictCode) {
                            "6000" -> phenomenonOptions = fullOptions
                            "7000" -> reasonOptions = fullOptions
                            "8000" -> handleTypeOptions = fullOptions
                            "9000" -> breakdownOptions = fullOptions
                        }
                        
                        val adapter = ClearableSpinnerAdapter(
                            this@RepairReportEditActivity,
                            R.layout.spinner_clear_item,
                            fullOptions.map { it.label },
                            { spinner.setSelection(0) }
                        )
                        adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                        spinner.adapter = adapter
                        
                        // 查找服务器选项在列表中的位置
                        var optionIndex = 0
                        for (i in fullOptions.indices) {
                            if (fullOptions[i].value == serverOption.value) {
                                optionIndex = i
                                break
                            }
                        }
                        
                        // 如果找到对应选项，设置选中状态
                        if (optionIndex > 0) {
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                try {
                                    spinner.setSelection(optionIndex)
                                    android.util.Log.d("RepairReport", "设置 ${fieldName} 选择成功: position=$optionIndex, value=${serverOption.value}")
                                } catch (e: Exception) {
                                    android.util.Log.e("RepairReport", "设置 ${fieldName} 选择异常", e)
                                }
                            }, 300)
                        } else {
                            android.util.Log.w("RepairReport", "${fieldName} 未找到匹配选项: ${serverOption.value}")
                        }
                    }
                }
                
                override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {
                    android.util.Log.e("RepairReport", "加载 ${fieldName} 选项失败", t)
                }
            })
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "设置 ${fieldName} 下拉框异常", e)
        }
    }

    private fun initPartsRecyclerViews() {
        // 客户配件列表
        rvCustomerParts = findViewById(R.id.rv_customer_parts)
        customerPartAdapter = PartAdapter(
            customerParts,
            onItemRemoved = { pos: Int ->
            if (pos >= 0 && pos < customerParts.size) {
                customerParts.removeAt(pos)
                customerPartAdapter.notifyDataSetChanged()
            }
            },
            type = "customer" // 指定为客户配件类型
        )
        rvCustomerParts.adapter = customerPartAdapter
        
        // 工程师配件列表
        rvEngineerParts = findViewById(R.id.rv_engineer_parts)
        engineerPartAdapter = PartAdapter(
            engineerParts,
            onItemRemoved = { pos: Int ->
            if (pos >= 0 && pos < engineerParts.size) {
                engineerParts.removeAt(pos)
                engineerPartAdapter.notifyDataSetChanged()
            }
            },
            type = "engineer" // 指定为工程师配件类型
        )
        rvEngineerParts.adapter = engineerPartAdapter
    }

    private fun selectImagesFromGallery(requestCode: Int) {
        val permissions = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            arrayOf(android.Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        if (permissions.any { ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED }) {
            pendingGalleryRequestCode = requestCode
            ActivityCompat.requestPermissions(this, permissions, REQUEST_STORAGE_PERMISSION)
            return
        }
        Matisse.from(this)
            .choose(MimeType.ofImage())
            .countable(true)
            .maxSelectable(20)
            .imageEngine(GlideEngine())
            .forResult(requestCode)
    }
    
    private fun takePhoto(requestCode: Int) {
        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            // 保存待处理的请求码
            pendingCameraRequestCode = requestCode
            // 请求相机权限
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                REQUEST_CAMERA_PERMISSION
            )
            return
        }

        Intent(MediaStore.ACTION_IMAGE_CAPTURE).also { takePictureIntent ->
            takePictureIntent.resolveActivity(packageManager)?.also {
                // 创建保存图片的文件
                val photoFile: File? = try {
                    createImageFile()
                } catch (ex: IOException) {
                    Toast.makeText(this, "创建图片文件失败", Toast.LENGTH_SHORT).show()
                    null
                }
                
                // 继续只有当文件成功创建
                photoFile?.also {
                    val photoURI: Uri = FileProvider.getUriForFile(
                        this,
                        "${BuildConfig.APPLICATION_ID}.fileprovider",
                        it
                    )
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                    startActivityForResult(takePictureIntent, requestCode)
                }
            }
        }
    }
    
    @Throws(IOException::class)
    private fun createImageFile(): File {
        // 创建图片文件名
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir: File? = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "JPEG_${timeStamp}_", /* 前缀 */
            ".jpg", /* 后缀 */
            storageDir /* 目录 */
        ).apply {
            // 保存文件路径用于后续使用
            currentPhotoPath = absolutePath
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        try {
        super.onActivityResult(requestCode, resultCode, data)
            
            // 首先处理取消情况
            if (resultCode != Activity.RESULT_OK) {
                // 如果用户取消了操作，不做任何处理，直接返回
                return
            }
            
            // 根据请求码处理不同的结果
            when (requestCode) {
                REQUEST_CODE_SELECT_CUSTOMER_PART -> {
                    // 处理客户配件选择结果
                    try {
                        // 检查数据有效性
                        if (data == null) {
                            Toast.makeText(this, "未获取到客户配件数据", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 安全获取序列化对象
                        val selectedPartsObj = try {
                            data.getSerializableExtra("selectedParts")
                        } catch (e: Exception) {
                            e.printStackTrace()
                            Toast.makeText(this, "客户配件数据格式错误", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 检查序列化对象是否为空
                        if (selectedPartsObj == null) {
                            Toast.makeText(this, "未获取到客户配件数据对象", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 尝试安全转换为列表类型
                        if (selectedPartsObj !is ArrayList<*>) {
                            Toast.makeText(this, "客户配件数据类型不是ArrayList", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 验证列表中的每个元素
                        val validParts = ArrayList<PartItem>()
                        for (item in selectedPartsObj) {
                            if (item is PartItem) {
                                validParts.add(item)
                            } else {
                                // 元素不是PartItem类型，记录调试信息但继续处理
                                println("忽略非PartItem类型元素: $item")
                            }
                        }
                        
                        // 如果没有有效部件，给出提示
                        if (validParts.isEmpty()) {
                            Toast.makeText(this, "未选择有效的客户配件", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 添加配件到列表
                        customerParts.addAll(validParts)
                        
                        // 检查适配器是否已初始化
                        if (!this::customerPartAdapter.isInitialized) {
                            Toast.makeText(this, "客户配件适配器未初始化", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 通知适配器数据已更改
                    customerPartAdapter.notifyDataSetChanged()
                        
                    } catch (e: Exception) {
                        e.printStackTrace()
                        Toast.makeText(this, "处理客户配件选择结果时发生异常: ${e.message}", Toast.LENGTH_SHORT).show()
                }
                }
                
                REQUEST_CODE_SELECT_ENGINEER_PART -> {
                    // 处理工程师配件选择结果
                    // 将整个处理逻辑包装在try-catch中
                    try {
                        // 检查数据有效性
                        if (data == null) {
                            Toast.makeText(this, "未获取到工程师配件数据", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 安全获取序列化对象
                        val selectedPartsObj = try {
                            data.getSerializableExtra("selectedParts")
                        } catch (e: Exception) {
                            e.printStackTrace()
                            Toast.makeText(this, "工程师配件数据格式错误", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 检查序列化对象是否为空
                        if (selectedPartsObj == null) {
                            Toast.makeText(this, "未获取到工程师配件数据对象", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 尝试安全转换为列表类型
                        if (selectedPartsObj !is ArrayList<*>) {
                            Toast.makeText(this, "工程师配件数据类型不是ArrayList", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 验证列表中的每个元素
                        val validParts = ArrayList<PartItem>()
                        for (item in selectedPartsObj) {
                            if (item is PartItem) {
                                validParts.add(item)
                            } else {
                                // 元素不是PartItem类型，记录调试信息但继续处理
                                println("忽略非PartItem类型元素: $item")
                            }
                        }
                        
                        // 如果没有有效部件，给出提示
                        if (validParts.isEmpty()) {
                            Toast.makeText(this, "未选择有效的工程师配件", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 添加配件到列表
                        engineerParts.addAll(validParts)
                        
                        // 检查适配器是否已初始化
                        if (!this::engineerPartAdapter.isInitialized) {
                            Toast.makeText(this, "工程师配件适配器未初始化", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        // 通知适配器数据已更改
                    engineerPartAdapter.notifyDataSetChanged()
                        
                    } catch (e: Exception) {
                        // 捕获并记录任何异常
                        e.printStackTrace()
                        Toast.makeText(this, "处理工程师配件选择结果时发生异常: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
                REQUEST_CODE_GALLERY_EXC_DESC -> {
                    try {
                        if (data == null) return
                        if (!this::excDescImageContainer.isInitialized) {
                            Toast.makeText(this, "故障描述图片容器未初始化", Toast.LENGTH_SHORT).show()
                            return
                        }
                        val uris = Matisse.obtainResult(data)
                        if (uris == null) {
                            Toast.makeText(this, "未获取到图片数据", Toast.LENGTH_SHORT).show()
                            return
                        }
                        if (uris.isNotEmpty()) {
                    excDescImageUris.addAll(uris)
                    showSelectedImages(excDescImageContainer, excDescImageUris)
                }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        Toast.makeText(this, "处理故障图片错误: $e", Toast.LENGTH_SHORT).show()
                    }
                }
                
                REQUEST_CODE_GALLERY_RESOLVE_DESC -> {
                    try {
                        if (data == null) return
                        if (!this::resolveDescImageContainer.isInitialized) {
                            Toast.makeText(this, "解决措施图片容器未初始化", Toast.LENGTH_SHORT).show()
                            return
                        }
                        val uris = Matisse.obtainResult(data)
                        if (uris == null) {
                            Toast.makeText(this, "未获取到图片数据", Toast.LENGTH_SHORT).show()
                            return
                        }
                        if (uris.isNotEmpty()) {
                    resolveDescImageUris.addAll(uris)
                    showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
                }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        Toast.makeText(this, "处理解决措施图片错误: $e", Toast.LENGTH_SHORT).show()
        }
    }

                REQUEST_CODE_CAMERA_EXC_DESC -> {
                    // 处理从相机拍摄的故障描述图片
                    try {
                        if (currentPhotoPath.isEmpty()) return
                        
                        val photoFile = File(currentPhotoPath)
                        if (!photoFile.exists()) {
                            Toast.makeText(this, "照片文件不存在", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        val fileUri = Uri.fromFile(photoFile)
                        if (this::excDescImageContainer.isInitialized) {
                            excDescImageUris.add(fileUri)
                            showSelectedImages(excDescImageContainer, excDescImageUris)
                        }
                        currentPhotoPath = ""
                    } catch (e: Exception) {
                        e.printStackTrace()
                        Toast.makeText(this, "处理拍摄图片错误: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
                
                REQUEST_CODE_CAMERA_RESOLVE_DESC -> {
                    // 处理从相机拍摄的解决措施图片
                    try {
                        if (currentPhotoPath.isEmpty()) return
                        
                        val photoFile = File(currentPhotoPath)
                        if (!photoFile.exists()) {
                            Toast.makeText(this, "照片文件不存在", Toast.LENGTH_SHORT).show()
                            return
                        }
                        
                        val fileUri = Uri.fromFile(photoFile)
                        if (this::resolveDescImageContainer.isInitialized) {
                            resolveDescImageUris.add(fileUri)
                            showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
                        }
                        currentPhotoPath = ""
                    } catch (e: Exception) {
                        e.printStackTrace()
                        Toast.makeText(this, "处理拍摄图片错误: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        } catch (e: Exception) {
            // 捕获onActivityResult整体的异常
            e.printStackTrace()
            Toast.makeText(this, "处理返回结果时发生异常: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showSelectedImages(container: LinearLayout, uris: List<Uri>) {
        // 清除现有视图，但保留第一个添加按钮
        if (container.childCount > 1) {
            container.removeViews(1, container.childCount - 1)
        } else if (container.childCount == 0) {
            val addButtonView = when (container) {
                excDescImageContainer -> layoutInflater.inflate(R.layout.item_add_image, container, false).apply {
                    val addButton = findViewById<ImageView>(R.id.btn_add_image)
                    // 优化添加按钮的触摸范围
                    optimizeAddButtonTouchArea(addButton)
                    addButton.setDebounceClickListener {
                        showImagePickerDialog(REQUEST_CODE_GALLERY_EXC_DESC, REQUEST_CODE_CAMERA_EXC_DESC)
                    }
                }
                resolveDescImageContainer -> layoutInflater.inflate(R.layout.item_add_image, container, false).apply {
                    val addButton = findViewById<ImageView>(R.id.btn_add_image)
                    // 优化添加按钮的触摸范围
                    optimizeAddButtonTouchArea(addButton)
                    addButton.setDebounceClickListener {
                        showImagePickerDialog(REQUEST_CODE_GALLERY_RESOLVE_DESC, REQUEST_CODE_CAMERA_RESOLVE_DESC)
                    }
                }
                else -> return
            }
            container.addView(addButtonView)
        }
        
        // 获取对应的URL列表
        val urls = if (container == excDescImageContainer) excDescImageUrls else resolveDescImageUrls
        
        // 添加所有图片视图
        for ((index, uri) in uris.withIndex()) {
            val frame = FrameLayout(this)
            // 统一尺寸为100dp，与添加按钮保持一致
            val size = (100 * resources.displayMetrics.density).toInt()
            val frameParams = LinearLayout.LayoutParams(size, size)
            frameParams.setMargins(4, 4, 4, 4)
            frame.layoutParams = frameParams

            val imageView = ImageView(this)
            imageView.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP
            
            // 如果URI是服务器图片（以content://server_image开头）且有对应的URL列表
            if (uri.toString().startsWith("content://server_image") && index < urls.size) {
                // 加载服务器URL
                Glide.with(this).load(urls[index]).into(imageView)
                android.util.Log.d("RepairReport", "加载服务器图片: ${urls[index]}")
            } else {
                // 加载本地URI
            Glide.with(this).load(uri).into(imageView)
                android.util.Log.d("RepairReport", "加载本地图片: $uri")
            }
            
            imageView.setDebounceClickListener {
                // 统一使用ImageViewerDialog预览图片，支持混合显示
                showUnifiedImagePreview(container, index)
            }

            val deleteBtn = ImageButton(this)
            // 优化删除按钮尺寸，调整为合适的大小
            val btnSize = (28 * resources.displayMetrics.density).toInt()
            val btnParams = FrameLayout.LayoutParams(btnSize, btnSize)
            btnParams.setMargins(0, 4, 4, 0)
            btnParams.gravity = Gravity.END or Gravity.TOP
            deleteBtn.layoutParams = btnParams
            deleteBtn.setImageResource(R.drawable.ic_delete_image)
            // 优化删除按钮的视觉效果 - 使用圆形背景
            deleteBtn.setBackgroundResource(R.drawable.bg_delete_button)
            deleteBtn.setColorFilter(Color.WHITE)
            deleteBtn.scaleType = ImageView.ScaleType.CENTER
            deleteBtn.contentDescription = "删除"
            // 添加适当的内边距确保图标居中
            val padding = (4 * resources.displayMetrics.density).toInt()
            deleteBtn.setPadding(padding, padding, padding, padding)
            // 添加触摸反馈
            deleteBtn.isClickable = true
            deleteBtn.isFocusable = true
            // 简化的触摸反馈 - 背景已处理按下状态
            deleteBtn.setOnTouchListener { v, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        v.scaleX = 0.9f
                        v.scaleY = 0.9f
                    }
                    android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                        v.scaleX = 1.0f
                        v.scaleY = 1.0f
                    }
                }
                false
            }
            
            // 为删除按钮添加触摸代理，扩大点击区域
            deleteBtn.post {
                val delegateArea = Rect()
                deleteBtn.getHitRect(delegateArea)
                // 扩大触摸区域12dp
                val extraSpace = (12 * resources.displayMetrics.density).toInt()
                delegateArea.top -= extraSpace
                delegateArea.bottom += extraSpace
                delegateArea.left -= extraSpace
                delegateArea.right += extraSpace
                frame.touchDelegate = TouchDelegate(delegateArea, deleteBtn)
            }
            deleteBtn.setDebounceClickListener {
                if (container == excDescImageContainer) {
                    // 如果是服务器图片，也要从URL列表中删除
                    if (uri.toString().startsWith("content://server_image") && index < excDescImageUrls.size) {
                        excDescImageUrls = excDescImageUrls.filterIndexed { i, _ -> i != index }
                    }
                    excDescImageUris.remove(uri)
                    showSelectedImages(excDescImageContainer, excDescImageUris)
                } else {
                    // 如果是服务器图片，也要从URL列表中删除
                    if (uri.toString().startsWith("content://server_image") && index < resolveDescImageUrls.size) {
                        resolveDescImageUrls = resolveDescImageUrls.filterIndexed { i, _ -> i != index }
                    }
                    resolveDescImageUris.remove(uri)
                    showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
                }
            }

            frame.addView(imageView)
            frame.addView(deleteBtn)
            container.addView(frame)
        }
    }

    /**
     * 统一的图片预览方法，支持混合显示服务器图片和本地图片
     */
    private fun showUnifiedImagePreview(container: LinearLayout, clickedIndex: Int) {
        try {
            // 根据容器类型获取对应的图片列表
            val (uris, urls) = when (container) {
                excDescImageContainer -> Pair(excDescImageUris, excDescImageUrls)
                resolveDescImageContainer -> Pair(resolveDescImageUris, resolveDescImageUrls)
                else -> {
                    Toast.makeText(this, "无法识别图片容器", Toast.LENGTH_SHORT).show()
                    return
                }
            }
            
            // 创建混合图片URL列表
            val imageUrls = mutableListOf<String>()
            
            // 遍历URI列表，将服务器图片和本地图片统一处理
            uris.forEachIndexed { index, uri ->
                if (uri.toString().startsWith("content://server_image") && index < urls.size) {
                    // 服务器图片，使用对应的URL
                    imageUrls.add(urls[index])
                } else {
                    // 本地图片，使用URI字符串
                    imageUrls.add(uri.toString())
                }
            }
            
            // 确保点击的索引有效
            val validPosition = if (clickedIndex >= 0 && clickedIndex < imageUrls.size) {
                clickedIndex
            } else {
                0
            }
            
            // 使用统一的ImageViewerDialog显示图片
            val dialog = ImageViewerDialog(this, imageUrls, validPosition)
            dialog.show()
            
            android.util.Log.d("RepairReport", "显示图片预览: 总计${imageUrls.size}张, 当前位置$validPosition")
            
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "显示图片预览失败", e)
            Toast.makeText(this, "图片预览失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun fetchOptions() {
        // 现象分类 6000
        workOrderApi.getDictOptions("6000").enqueue(object : Callback<ApiResponse<List<Option>>> {
            override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                val list = response.body()?.data ?: listOf()
                phenomenonOptions = listOf(Option("", "请选择")) + list
                val adapter = ClearableSpinnerAdapter(
                    this@RepairReportEditActivity,
                    R.layout.spinner_clear_item,
                    phenomenonOptions.map { it.label },
                    { spinnerPhenomenon.setSelection(0) }
                )
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                spinnerPhenomenon.adapter = adapter
            }
            override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {}
        })
        // 原因分类 7000
        workOrderApi.getDictOptions("7000").enqueue(object : Callback<ApiResponse<List<Option>>> {
            override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                val list = response.body()?.data ?: listOf()
                reasonOptions = listOf(Option("", "请选择")) + list
                val adapter = ClearableSpinnerAdapter(
                    this@RepairReportEditActivity,
                    R.layout.spinner_clear_item,
                    reasonOptions.map { it.label },
                    { spinnerReason.setSelection(0) }
                )
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                spinnerReason.adapter = adapter
            }
            override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {}
        })
        // 处理类型 8000
        workOrderApi.getDictOptions("8000").enqueue(object : Callback<ApiResponse<List<Option>>> {
            override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                val list = response.body()?.data ?: listOf()
                handleTypeOptions = listOf(Option("", "请选择")) + list
                val adapter = ClearableSpinnerAdapter(
                    this@RepairReportEditActivity,
                    R.layout.spinner_clear_item,
                    handleTypeOptions.map { it.label },
                    { spinnerHandleType.setSelection(0) }
                )
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                spinnerHandleType.adapter = adapter
            }
            override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {}
        })
        // 故障组件 9000
        workOrderApi.getDictOptions("9000").enqueue(object : Callback<ApiResponse<List<Option>>> {
            override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                val list = response.body()?.data ?: listOf()
                breakdownOptions = listOf(Option("", "请选择")) + list
                val adapter = ClearableSpinnerAdapter(
                    this@RepairReportEditActivity,
                    R.layout.spinner_clear_item,
                    breakdownOptions.map { it.label },
                    { spinnerBreakdown.setSelection(0) }
                )
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                spinnerBreakdown.adapter = adapter
            }
            override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {}
        })
    }

    private fun saveDraft() {
        // 收集表单数据
        val draft = HashMap<String, Any?>()
        draft["orderId"] = orderId
        draft["customerId"] = customerId
        draft["deviceGroupId"] = deviceGroupId
        draft["excDesc"] = etContent.text.toString()
        draft["excDescImageUris"] = excDescImageUris.map { it.toString() }
        draft["resolveDesc"] = etResult.text.toString()
        draft["resolveDescImageUris"] = resolveDescImageUris.map { it.toString() }
        draft["announcements"] = etAnnouncements.text.toString()
        draft["blackWhiteCount"] = etBlackWhiteCount.text.toString()
        draft["colorCount"] = etColorCount.text.toString()
        draft["fiveColorCount"] = etFiveColorCount.text.toString()
        draft["blackWhiteExclude"] = etBlackWhiteExclude.text.toString()
        draft["colorExclude"] = etColorExclude.text.toString()
        draft["fiveColorExclude"] = etFiveColorExclude.text.toString()
        draft["phenomenonIndex"] = spinnerPhenomenon.selectedItemPosition
        draft["reasonIndex"] = spinnerReason.selectedItemPosition
        draft["handleTypeIndex"] = spinnerHandleType.selectedItemPosition
        draft["breakdownIndex"] = spinnerBreakdown.selectedItemPosition
        
        // 保存零件信息时确保转换为可序列化格式
        // 客户零件
        val customerPartsData = customerParts.map { part ->
            val map = HashMap<String, Any?>()
            map["id"] = part.id
            map["num"] = part.num  // 保存总库存数量
            map["changeNum"] = part.changeNum  // 明确保存更换数量
            map["location"] = part.location?.toString() ?: ""
            map["itemName"] = part.itemName
            map["skuInfo"] = part.skuInfo
            map["oemNumber"] = part.oemNumber ?: ""
            map["saleUnitPrice"] = part.saleUnitPrice
            map["articleCode"] = part.articleCode // 添加articleCode用于分组计算
            map["batchCode"] = part.batchCode // 添加batchCode用于分组计算
            map
        }
        draft["customerParts"] = customerPartsData
        
        // 工程师零件
        val engineerPartsData = engineerParts.map { part ->
            val map = HashMap<String, Any?>()
            map["id"] = part.id
            map["num"] = part.num  // 保存总库存数量
            map["changeNum"] = part.changeNum  // 明确保存更换数量
            map["location"] = part.location?.toString() ?: ""
            map["itemName"] = part.itemName
            map["skuInfo"] = part.skuInfo
            map["oemNumber"] = part.oemNumber ?: ""
            map["saleUnitPrice"] = part.saleUnitPrice
            map["articleCode"] = part.articleCode // 添加articleCode用于分组计算
            map["batchCode"] = part.batchCode // 添加batchCode用于分组计算
            map
        }
        draft["engineerParts"] = engineerPartsData
        
        draft["timestamp"] = System.currentTimeMillis()
        
        // 记录草稿内容日志
        android.util.Log.d("RepairReport", "草稿数据: ${Gson().toJson(draft)}")

        // 显示保存进度对话框
        val dialog = ProgressDialog.show(this, null, "正在保存草稿...", true, false)
        
        // 保存到服务器
        saveToServer(draft, dialog)
    }
    
    /**
     * 保存草稿到服务器
     * 使用WorkOrderApi中的savePopReport接口
     */
    private fun saveToServer(draftData: HashMap<String, Any?>, dialog: ProgressDialog? = null) {
        try {
            // 过滤出需要上传的本地图片URI（排除虚拟URI）
            val localExcDescImageUris = excDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
            val localResolveDescImageUris = resolveDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
            
            android.util.Log.d("RepairReport", "本地故障描述图片: ${localExcDescImageUris.size}张, 服务器图片: ${excDescImageUrls.size}张")
            android.util.Log.d("RepairReport", "本地解决措施图片: ${localResolveDescImageUris.size}张, 服务器图片: ${resolveDescImageUrls.size}张")
            
            // 如果有本地图片需要上传，先上传图片再保存草稿
            if (localExcDescImageUris.isNotEmpty() || localResolveDescImageUris.isNotEmpty()) {
                // 显示上传进度
                val uploadingDialog = dialog ?: ProgressDialog.show(this, null, "正在上传图片...", true, false)
                
                // 上传故障描述图片
                uploadAllImagesCos(localExcDescImageUris, { newExcUrls ->
                    // 上传解决措施图片
                    uploadAllImagesCos(localResolveDescImageUris, { newResolveUrls ->
                        // 优化URL合并逻辑，避免重复
                        updateImageUrls(newExcUrls, newResolveUrls)
                        
                        // 使用上传后的URL保存草稿
                        doSaveToServer(draftData, uploadingDialog)
                        
                    }, { error ->
                        runOnUiThread {
                            uploadingDialog.dismiss()
                            Toast.makeText(this@RepairReportEditActivity, "解决措施图片上传失败: $error", Toast.LENGTH_SHORT).show()
                        }
                    })
                }, { error ->
                    runOnUiThread {
                        uploadingDialog.dismiss()
                        Toast.makeText(this@RepairReportEditActivity, "故障描述图片上传失败: $error", Toast.LENGTH_SHORT).show()
                    }
                })
            } else {
                // 没有本地图片需要上传，直接保存草稿
                doSaveToServer(draftData, dialog)
            }
        } catch (e: Exception) {
            dialog?.dismiss()
            android.util.Log.e("RepairReport", "准备服务器参数失败: ${e.message}", e)
            Toast.makeText(this, "本地草稿已保存，但同步到服务器失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 优化的图片URL更新逻辑，避免重复
     */
    private fun updateImageUrls(newExcUrls: List<String>, newResolveUrls: List<String>) {
        // 只添加新上传的URL，避免重复现有的服务器URL
        if (newExcUrls.isNotEmpty()) {
            val updatedExcUrls = excDescImageUrls.toMutableList()
            updatedExcUrls.addAll(newExcUrls)
            excDescImageUrls = updatedExcUrls
            android.util.Log.d("RepairReport", "添加新故障描述图片URL: ${newExcUrls.size}张, 总计: ${excDescImageUrls.size}张")
        }
        
        if (newResolveUrls.isNotEmpty()) {
            val updatedResolveUrls = resolveDescImageUrls.toMutableList()
            updatedResolveUrls.addAll(newResolveUrls)
            resolveDescImageUrls = updatedResolveUrls
            android.util.Log.d("RepairReport", "添加新解决措施图片URL: ${newResolveUrls.size}张, 总计: ${resolveDescImageUrls.size}张")
        }
        
        // 清理重复URL
        deduplicateImageUrls()
        
        // 重建虚拟URI列表，确保与URL列表一致
        rebuildVirtualUriLists()
        
        // 确保UI更新在主线程中执行
        runOnUiThread {
            // 更新界面显示
            if (newExcUrls.isNotEmpty() && this::excDescImageContainer.isInitialized) {
                showSelectedImages(excDescImageContainer, excDescImageUris)
            }
            if (newResolveUrls.isNotEmpty() && this::resolveDescImageContainer.isInitialized) {
                showSelectedImages(resolveDescImageContainer, resolveDescImageUris)
            }
        }
    }
    
    /**
     * 上传后同步URI列表
     */
    private fun syncUriListsAfterUpload(newExcCount: Int, newResolveCount: Int) {
        // 移除已上传的本地URI，添加对应数量的虚拟URI
        val localExcUris = excDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        val localResolveUris = resolveDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        
        // 更新故障描述图片URI列表
        if (newExcCount > 0 && localExcUris.size >= newExcCount) {
            val remainingLocalExcUris = localExcUris.drop(newExcCount)
            val serverExcUris = excDescImageUris.filter { it.toString().startsWith("content://server_image_") }
            val newServerUris = (0 until newExcCount).map { 
                Uri.parse("content://server_image_exc_${excDescImageUrls.size - newExcCount + it}_${System.currentTimeMillis()}")
            }
            excDescImageUris.clear()
            excDescImageUris.addAll(serverExcUris + newServerUris + remainingLocalExcUris)
        }
        
        // 更新解决措施图片URI列表
        if (newResolveCount > 0 && localResolveUris.size >= newResolveCount) {
            val remainingLocalResolveUris = localResolveUris.drop(newResolveCount)
            val serverResolveUris = resolveDescImageUris.filter { it.toString().startsWith("content://server_image_") }
            val newServerUris = (0 until newResolveCount).map { 
                Uri.parse("content://server_image_resolve_${resolveDescImageUrls.size - newResolveCount + it}_${System.currentTimeMillis()}")
            }
            resolveDescImageUris.clear()
            resolveDescImageUris.addAll(serverResolveUris + newServerUris + remainingLocalResolveUris)
        }
    }
    
    /**
     * 清理重复的图片URL
     */
    private fun deduplicateImageUrls() {
        // 去重故障描述图片URL
        val uniqueExcUrls = excDescImageUrls.distinct()
        if (uniqueExcUrls.size != excDescImageUrls.size) {
            android.util.Log.d("RepairReport", "故障描述图片URL去重: ${excDescImageUrls.size} -> ${uniqueExcUrls.size}")
            excDescImageUrls = uniqueExcUrls
        }
        
        // 去重解决措施图片URL  
        val uniqueResolveUrls = resolveDescImageUrls.distinct()
        if (uniqueResolveUrls.size != resolveDescImageUrls.size) {
            android.util.Log.d("RepairReport", "解决措施图片URL去重: ${resolveDescImageUrls.size} -> ${uniqueResolveUrls.size}")
            resolveDescImageUrls = uniqueResolveUrls
        }
    }
    
    /**
     * 创建稳定的虚拟URI，基于URL索引而不是时间戳
     */
    private fun createStableVirtualUri(type: String, index: Int): Uri {
        return Uri.parse("content://server_image_${type}_${index}")
    }
    
    /**
     * 重建虚拟URI列表，确保与URL列表一致
     */
    private fun rebuildVirtualUriLists() {
        // 重建故障描述图片的虚拟URI列表
        val localExcUris = excDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        val serverExcUris = (0 until excDescImageUrls.size).map { 
            createStableVirtualUri("exc", it)
        }
        excDescImageUris.clear()
        excDescImageUris.addAll(serverExcUris + localExcUris)
        
        // 重建解决措施图片的虚拟URI列表
        val localResolveUris = resolveDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        val serverResolveUris = (0 until resolveDescImageUrls.size).map { 
            createStableVirtualUri("resolve", it)
        }
        resolveDescImageUris.clear()
        resolveDescImageUris.addAll(serverResolveUris + localResolveUris)
        
        android.util.Log.d("RepairReport", "重建虚拟URI: 故障描述${serverExcUris.size}+${localExcUris.size}, 解决措施${serverResolveUris.size}+${localResolveUris.size}")
    }
    
    /**
     * 实际执行保存到服务器的操作
     */
    private fun doSaveToServer(draftData: HashMap<String, Any?>, dialog: ProgressDialog? = null) {
        try {
            // 准备API参数
            val serverParams = HashMap<String, Any>()
            val engineerId = getSharedPreferences("token_pref", MODE_PRIVATE).getString("engineerId", "") ?: ""
            
            // 基本信息
            serverParams["engineerId"] = engineerId
            serverParams["workOrderId"] = orderId ?: ""
            serverParams["excDesc"] = draftData["excDesc"] as? String ?: ""
            serverParams["resolveDesc"] = draftData["resolveDesc"] as? String ?: ""
            serverParams["announcements"] = draftData["announcements"] as? String ?: ""
            
            // 图片信息处理 - 使用已上传的图片URL
            serverParams["excDescPics"] = excDescImageUrls.map { CosObject(it) }
            serverParams["resolveDescPics"] = resolveDescImageUrls.map { CosObject(it) }
            
            // 分类选项
            val phenomenonIndex = draftData["phenomenonIndex"] as? Int ?: 0
            val reasonIndex = draftData["reasonIndex"] as? Int ?: 0
            val handleTypeIndex = draftData["handleTypeIndex"] as? Int ?: 0
            val breakdownIndex = draftData["breakdownIndex"] as? Int ?: 0
            
            serverParams["excType"] = phenomenonOptions.getOrNull(phenomenonIndex) ?: Option("", "")
            serverParams["reasonType"] = reasonOptions.getOrNull(reasonIndex) ?: Option("", "")
            serverParams["resolveType"] = handleTypeOptions.getOrNull(handleTypeIndex) ?: Option("", "")
            serverParams["excUnit"] = breakdownOptions.getOrNull(breakdownIndex) ?: Option("", "")
            
            // 计数器数据
            serverParams["blackWhiteCount"] = draftData["blackWhiteCount"] as? String ?: ""
            serverParams["blackWhiteExclude"] = draftData["blackWhiteExclude"] as? String ?: ""
            serverParams["colorCount"] = draftData["colorCount"] as? String ?: ""
            serverParams["colorExclude"] = draftData["colorExclude"] as? String ?: ""
            serverParams["fiveColourCount"] = draftData["fiveColorCount"] as? String ?: ""
            serverParams["fiveColorExclude"] = draftData["fiveColorExclude"] as? String ?: ""
            
            // 计算印量
            try {
                val blackWhite = (draftData["blackWhiteCount"] as? String)?.toIntOrNull() ?: 0
                val color = (draftData["colorCount"] as? String)?.toIntOrNull() ?: 0
                val fiveColor = (draftData["fiveColorCount"] as? String)?.toIntOrNull() ?: 0
                serverParams["printCount"] = (blackWhite + color + fiveColor).toString()
            } catch (e: Exception) {
                serverParams["printCount"] = "0"
            }
            
            // 零件替换列表 - 从草稿数据中提取
            val allParts = mutableListOf<Map<String, Any>>()
            
            // 处理客户零件
            val customerPartsList = draftData["customerParts"] as? List<*> ?: emptyList<Any>()
            android.util.Log.d("RepairReport", "处理客户零件列表: ${customerPartsList.size}个")
            
            // 记录完整的零件数据结构，帮助调试
            if (customerPartsList.isNotEmpty()) {
                val firstPart = customerPartsList.first()
                android.util.Log.d("RepairReport", "客户零件数据结构示例: ${Gson().toJson(firstPart)}")
            }
            
            for (part in customerPartsList) {
                if (part is Map<*, *>) {
                    val id = part["id"]?.toString() ?: continue
                    
                    // 尝试获取changeNum字段，如果不存在则尝试使用num字段
                    val changeNum = when {
                        part.containsKey("changeNum") -> part["changeNum"] as? Number
                        else -> part["num"] as? Number
                    } ?: 0
                    
                    if (changeNum.toInt() > 0) {
                        val location = part["location"]?.toString() ?: ""
                        val partMap = mapOf(
                            "itemStoreId" to id,
                            "num" to changeNum,
                            "location" to listOf(location)  // 确保location是列表格式
                        )
                        allParts.add(partMap)
                        android.util.Log.d("RepairReport", "添加客户零件: $partMap")
                    }
                }
            }
            
            // 处理工程师零件
            val engineerPartsList = draftData["engineerParts"] as? List<*> ?: emptyList<Any>()
            android.util.Log.d("RepairReport", "处理工程师零件列表: ${engineerPartsList.size}个")
            
            // 记录完整的零件数据结构，帮助调试
            if (engineerPartsList.isNotEmpty()) {
                val firstPart = engineerPartsList.first()
                android.util.Log.d("RepairReport", "工程师零件数据结构示例: ${Gson().toJson(firstPart)}")
            }
            
            for (part in engineerPartsList) {
                if (part is Map<*, *>) {
                    val id = part["id"]?.toString() ?: continue
                    
                    // 尝试获取changeNum字段，如果不存在则尝试使用num字段
                    val changeNum = when {
                        part.containsKey("changeNum") -> part["changeNum"] as? Number
                        else -> part["num"] as? Number
                    } ?: 0
                    
                    if (changeNum.toInt() > 0) {
                        val location = part["location"]?.toString() ?: ""
                        val partMap = mapOf(
                            "itemStoreId" to id,
                            "num" to changeNum,
                            "location" to listOf(location)  // 确保location是列表格式
                        )
                        allParts.add(partMap)
                        android.util.Log.d("RepairReport", "添加工程师零件: $partMap")
                    }
                }
            }
            
            serverParams["replaceInfoList"] = allParts
            
            // 记录日志
            android.util.Log.d("RepairReport", "保存草稿到服务器，参数: ${Gson().toJson(serverParams)}")
            
            // 调用API保存草稿
            workOrderApi.savePopReport(serverParams).enqueue(object : Callback<ApiResponse<Any>> {
                override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                    dialog?.dismiss()
                    
                    if (response.isSuccessful && response.body()?.code == 200) {
                        android.util.Log.d("RepairReport", "草稿同步到服务器成功")
                        // 将同步时间保存到SharePreferences
                        val sp = getSharedPreferences("repair_report_sync", MODE_PRIVATE)
                        sp.edit().putLong("sync_${orderId}", System.currentTimeMillis()).apply()
                        
                        Toast.makeText(this@RepairReportEditActivity, "草稿已保存", Toast.LENGTH_SHORT).show()
                    } else {
                        android.util.Log.e("RepairReport", "草稿同步到服务器失败: ${response.body()?.msg}")
                        Toast.makeText(this@RepairReportEditActivity, "本地草稿已保存，但同步到服务器失败: ${response.body()?.msg ?: "网络错误"}", Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                    dialog?.dismiss()
                    android.util.Log.e("RepairReport", "草稿同步到服务器失败: ${t.message}", t)
                    Toast.makeText(this@RepairReportEditActivity, "本地草稿已保存，但同步到服务器失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
            
        } catch (e: Exception) {
            dialog?.dismiss()
            android.util.Log.e("RepairReport", "准备服务器参数失败: ${e.message}", e)
            Toast.makeText(this, "本地草稿已保存，但同步到服务器失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun confirmSubmit() {
        AlertDialog.Builder(this)
            .setTitle("确认提交")
            .setMessage("请确认信息无误，提交后不可修改。")
            .setPositiveButton("确认") { _, _ -> submitReport() }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun submitReport() {
        // 表单校验 - 按照zhijian项目的校验逻辑
        if (etContent.text.isNullOrEmpty()) {
            Toast.makeText(this, "请填写故障描述", Toast.LENGTH_SHORT).show(); return
        }
        if (excDescImageUris.isEmpty()) {
            Toast.makeText(this, "请添加故障描述图片", Toast.LENGTH_SHORT).show(); return
        }
        if (etResult.text.isNullOrEmpty()) {
            Toast.makeText(this, "请填写解决措施", Toast.LENGTH_SHORT).show(); return
        }
        if (resolveDescImageUris.isEmpty()) {
            Toast.makeText(this, "请添加解决措施图片", Toast.LENGTH_SHORT).show(); return
        }
        if (etAnnouncements.text.isNullOrEmpty()) {
            Toast.makeText(this, "请填写下次注意事项", Toast.LENGTH_SHORT).show(); return
        }
        if (spinnerPhenomenon.selectedItemPosition == 0) {
            Toast.makeText(this, "请选择现象分类", Toast.LENGTH_SHORT).show(); return
        }
        if (spinnerReason.selectedItemPosition == 0) {
            Toast.makeText(this, "请选择原因分类", Toast.LENGTH_SHORT).show(); return
        }
        if (spinnerHandleType.selectedItemPosition == 0) {
            Toast.makeText(this, "请选择处理类型", Toast.LENGTH_SHORT).show(); return
        }
        if (spinnerBreakdown.selectedItemPosition == 0) {
            Toast.makeText(this, "请选择故障组件", Toast.LENGTH_SHORT).show(); return
        }
        
        // 零件校验 - 基于 articleCode + batchCode 组合
        val customerParts = customerPartAdapter.getAllItems()
        val engineerParts = engineerPartAdapter.getAllItems()

        // 分别校验客户零件和工程师零件
        // 1. 校验客户零件
        if (customerParts.isNotEmpty()) {
            val customerGrouped = customerParts.groupBy { "${it.articleCode ?: ""}|${it.batchCode ?: ""}" }
            for ((key, list) in customerGrouped) {
                val totalChange = list.sumOf { it.changeNum }
                val stock = list.firstOrNull()?.num ?: 0
                if (totalChange > stock) {
                    val firstPart = list.firstOrNull()
                    val partName = firstPart?.itemName ?: firstPart?.articleCode ?: "未知零件"
                    val batchCode = firstPart?.batchCode ?: "无"
                    Toast.makeText(this, "客户零件[${partName}]批次[${batchCode}]更换数量不能超过剩余数量", Toast.LENGTH_SHORT).show()
                    return
                }
            }
        }

        // 2. 校验工程师零件
        if (engineerParts.isNotEmpty()) {
            val engineerGrouped = engineerParts.groupBy { "${it.articleCode ?: ""}|${it.batchCode ?: ""}" }
            for ((key, list) in engineerGrouped) {
                val totalChange = list.sumOf { it.changeNum }
                val stock = list.firstOrNull()?.num ?: 0
                if (totalChange > stock) {
                    val firstPart = list.firstOrNull()
                    val partName = firstPart?.itemName ?: firstPart?.articleCode ?: "未知零件"
                    val batchCode = firstPart?.batchCode ?: "无"
                    Toast.makeText(this, "工程师零件[${partName}]批次[${batchCode}]更换数量不能超过剩余数量", Toast.LENGTH_SHORT).show()
                    return
                }
            }
        }

        // 收集所有表单数据，组装参数，调用API提交
        val dialog = ProgressDialog.show(this, null, "正在保存草稿...", true, false)

        // 检查 TransferManager 是否已初始化
        if (transferManager == null) {
            dialog.dismiss()
            Toast.makeText(this, "图片上传服务未就绪，请稍后重试", Toast.LENGTH_SHORT).show()
            initTransferManagerWithRetry() // 尝试重新初始化
            return
        }
        
        // 过滤出需要上传的本地图片URI（排除虚拟URI）
        val localExcDescImageUris = excDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        val localResolveDescImageUris = resolveDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        
        android.util.Log.d("RepairReport", "提交报告 - 本地故障描述图片: ${localExcDescImageUris.size}张, 服务器图片: ${excDescImageUrls.size}张")
        android.util.Log.d("RepairReport", "提交报告 - 本地解决措施图片: ${localResolveDescImageUris.size}张, 服务器图片: ${resolveDescImageUrls.size}张")
        
        // 上传所有本地图片 - 使用 COS SDK
        uploadAllImagesCos(localExcDescImageUris, { newExcUrls ->
            uploadAllImagesCos(localResolveDescImageUris, { newResolveUrls ->
                // 使用统一的URL更新逻辑，避免重复
                updateImageUrls(newExcUrls, newResolveUrls)
                
                doSubmitReport(dialog) // 图片上传成功后，提交报告
            }, { msg ->
                runOnUiThread {
                    dialog.dismiss()
                    Toast.makeText(this, "解决措施图片上传失败: $msg", Toast.LENGTH_SHORT).show()
                }
            })
        }, { msg ->
            runOnUiThread {
                dialog.dismiss()
                Toast.makeText(this, "故障描述图片上传失败: $msg", Toast.LENGTH_SHORT).show()
            }
        })
    }

    // 使用 COS SDK 批量上传图片
    private fun uploadAllImagesCos(
        localUris: List<Uri>,
        callback: (List<String>) -> Unit,
        onFail: (String) -> Unit
    ) {
        val tm = transferManager // 获取成员变量
        val bucketInfo = RepairOrderApp.cosBucketInfo
        if (tm == null || bucketInfo == null) {
            val errorMsg = if (tm == null) "TransferManager 未初始化" else "COS Bucket 信息未获取"
            android.util.Log.e("RepairReport", errorMsg)
            onFail("图片上传服务配置错误: $errorMsg")
            // 如果 tm 为 null，尝试重新初始化
            if (tm == null) initTransferManagerWithRetry()
                return
            }

        if (localUris.isEmpty()) {
            callback(emptyList())
            return
        }

        val totalCount = localUris.size
        val successCount = AtomicInteger(0)
        val failureCount = AtomicInteger(0)
        // 使用 ConcurrentHashMap 存储 URL，确保线程安全
        val uploadedUrls = ConcurrentHashMap<Int, String>()
        val errorMessages = ConcurrentHashMap<Int, String>()

        localUris.forEachIndexed { index, uri ->
            var inputStream: InputStream? = null
            try {
                // 1. 获取 InputStream 和原始文件名
                inputStream = contentResolver.openInputStream(uri)
                if (inputStream == null) {
                    throw IOException("无法打开图片流: $uri")
                }

                // 2. 图片压缩 (保留现有逻辑)
                val options = BitmapFactory.Options().apply { inJustDecodeBounds = false }
                val bitmap = BitmapFactory.decodeStream(inputStream, null, options)
                    ?: throw IOException("图片解码失败: $uri")
                inputStream.close() // 关闭原始流

                val maxDim = 1280
                val (newW, newH) = if (bitmap.width > maxDim || bitmap.height > maxDim) {
                    val scale = Math.min(maxDim.toFloat() / bitmap.width, maxDim.toFloat() / bitmap.height)
                    Pair((bitmap.width * scale).toInt(), (bitmap.height * scale).toInt())
                        } else {
                    Pair(bitmap.width, bitmap.height)
                        }
                val scaledBitmap = if (bitmap.width != newW || bitmap.height != newH) {
                    Bitmap.createScaledBitmap(bitmap, newW, newH, true)
                    } else {
                    bitmap
                }
                val baos = ByteArrayOutputStream()
                scaledBitmap.compress(Bitmap.CompressFormat.JPEG, 80, baos)
                val compressedBytes = baos.toByteArray()
                baos.close()
                if (scaledBitmap != bitmap) scaledBitmap.recycle()
                if (bitmap != scaledBitmap) bitmap.recycle() // 确保原始 bitmap 也被回收

                // 3. 生成 COS 对象键 (ObjectKey)
                val sdf = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
                val datePath = sdf.format(Date())
                val uniqueId = UUID.randomUUID().toString()
                val cosPath = "${bucketInfo.prefix}$datePath/$uniqueId.jpg" // 例如: repair_images/2023/11/15/uuid.jpg

                // 4. 将压缩后的字节流写入临时文件
                val tempFile = File.createTempFile("upload_${uniqueId}", ".jpg", cacheDir)
                FileOutputStream(tempFile).use { it.write(compressedBytes) }

                // 5. 创建上传任务（用文件路径）
                val uploadTask = tm.upload(bucketInfo.bucket, cosPath, tempFile.absolutePath, null)

                // 6. 设置上传监听
                uploadTask.setTransferStateListener(object : TransferStateListener {
                    override fun onStateChanged(state: TransferState?) {
                        android.util.Log.d("RepairReport", "图片 ${index + 1} 上传状态: $state")
                    }
                })

                uploadTask.setCosXmlResultListener(object : CosXmlResultListener {
                    override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                        // val url = tm.cosXmlService.getCosUrl(bucketInfo.bucket, cosPath)
                        val url = "https://${bucketInfo.bucket}.cos.${bucketInfo.region}.myqcloud.com/$cosPath"
                        android.util.Log.i("RepairReport", "图片 ${index + 1} 上传成功: $url")
                        uploadedUrls[index] = url // 存储 URL
                        tempFile.delete() // 上传成功后删除临时文件
                        if (successCount.incrementAndGet() + failureCount.get() == totalCount) {
                            // 所有任务完成
                            if (failureCount.get() == 0) {
                                val sortedUrls = uploadedUrls.entries.sortedBy { it.key }.map { it.value }
                                callback(sortedUrls)
                            } else {
                                val firstErrorIndex = errorMessages.keys.minOrNull() ?: -1
                                val firstErrorMsg = errorMessages[firstErrorIndex] ?: "未知上传错误"
                                onFail(firstErrorMsg)
                    }
                }
                    }

                    override fun onFail(request: CosXmlRequest?, clientException: CosXmlClientException?, serviceException: CosXmlServiceException?) {
                        val errorMsg = clientException?.message ?: serviceException?.errorMessage ?: "未知上传错误"
                        android.util.Log.e("RepairReport", "图片 ${index + 1} 上传失败: $errorMsg", clientException ?: serviceException)
                        errorMessages[index] = "图片 ${index + 1} 上传失败: $errorMsg"
                        tempFile.delete() // 上传失败也要删除临时文件
                        if (failureCount.incrementAndGet() == 1) {
                            onFail(errorMessages[index] ?: errorMsg)
                        } else if (successCount.get() + failureCount.get() == totalCount) {
                            val firstErrorIndex = errorMessages.keys.minOrNull() ?: -1
                            val firstErrorMsg = errorMessages[firstErrorIndex] ?: "未知上传错误"
                            onFail(firstErrorMsg)
                        }
                }
            })

            } catch (e: Exception) {
                android.util.Log.e("RepairReport", "处理图片 ${index + 1} 时发生异常: ${e.message}", e)
                errorMessages[index] = "处理图片 ${index + 1} 失败: ${e.message}"
                if (failureCount.incrementAndGet() == 1) {
                     onFail(errorMessages[index] ?: e.message ?: "处理图片失败")
                } else if (successCount.get() + failureCount.get() == totalCount) {
                     val firstErrorIndex = errorMessages.keys.minOrNull() ?: -1
                     val firstErrorMsg = errorMessages[firstErrorIndex] ?: "未知上传错误"
                     onFail(firstErrorMsg)
                }
            } finally {
                try {
                    inputStream?.close() // 确保输入流被关闭
                } catch (ioe: IOException) {
                    android.util.Log.e("RepairReport", "关闭 InputStream 时出错", ioe)
                }
            }
        }
    }

    // 组装参数并调用API
    private fun doSubmitReport(dialog: ProgressDialog) {
        val params = HashMap<String, Any>()
        val engineerId = getSharedPreferences("token_pref", MODE_PRIVATE).getString("engineerId", "") ?: ""
        android.util.Log.d("RepairReport", "engineerId = $engineerId")
        
        // 参数组装 - 完全遵循zhijian项目的参数结构
        params["engineerId"] = engineerId
        params["workOrderId"] = orderId ?: ""
        params["excDesc"] = etContent.text.toString().trim()
        params["excDescPics"] = excDescImageUrls.map { CosObject(it) }
        params["resolveDesc"] = etResult.text.toString().trim()
        params["resolveDescPics"] = resolveDescImageUrls.map { CosObject(it) }
        params["announcements"] = etAnnouncements.text.toString().trim()
        
        // 分类字段传完整对象（含label和value）
        params["excType"] = phenomenonOptions.getOrNull(spinnerPhenomenon.selectedItemPosition) ?: Option("", "")
        params["reasonType"] = reasonOptions.getOrNull(spinnerReason.selectedItemPosition) ?: Option("", "")
        params["resolveType"] = handleTypeOptions.getOrNull(spinnerHandleType.selectedItemPosition) ?: Option("", "")
        params["excUnit"] = breakdownOptions.getOrNull(spinnerBreakdown.selectedItemPosition) ?: Option("", "")
        
        // 计数器相关字段
        params["blackWhiteCount"] = etBlackWhiteCount.text.toString()
        params["blackWhiteExclude"] = etBlackWhiteExclude.text.toString()
        params["colorCount"] = etColorCount.text.toString()
        params["colorExclude"] = etColorExclude.text.toString()
        params["fiveColourCount"] = etFiveColorCount.text.toString()
        params["fiveColorExclude"] = etFiveColorExclude.text.toString()
        
        // 计算上次维修后到目前的印量 - 与zhijian一致
        try {
            val blackWhite = etBlackWhiteCount.text.toString().toIntOrNull() ?: 0
            val color = etColorCount.text.toString().toIntOrNull() ?: 0
            val fiveColor = etFiveColorCount.text.toString().toIntOrNull() ?: 0
            params["printCount"] = (blackWhite + color + fiveColor).toString()
        } catch (e: Exception) {
            params["printCount"] = "0"
        }
        
        // 零件更换列表 - 与zhijian保持一致的结构
        val allParts = mutableListOf<Map<String, Any>>()
        
        // 客户配件
        customerParts.forEach { part ->
            if (part.changeNum > 0) {
                allParts.add(mapOf(
                    "itemStoreId" to part.id,
                    "num" to part.changeNum,
                    // location 字段始终封装为数组
                    "location" to listOf(part.location ?: "")
                ))
            }
        }
        
        // 工程师配件
        engineerParts.forEach { part ->
            if (part.changeNum > 0) {
                allParts.add(mapOf(
                    "itemStoreId" to part.id,
                    "num" to part.changeNum,
                    // location 字段始终封装为数组
                    "location" to listOf(part.location ?: "")
                ))
            }
        }
        
        params["replaceInfoList"] = allParts
        
        android.util.Log.d("RepairReport", "直接提交报告，参数: ${Gson().toJson(params)}")
        
        // 直接调用提交报告接口
        submitReportToServer(params, dialog)
    }
    
    // 提取提交报告的逻辑到单独的方法
    private fun submitReportToServer(params: HashMap<String, Any>, dialog: ProgressDialog) {
        // 更新进度对话框内容
        dialog.setMessage("正在提交报告...")
        
        android.util.Log.d("RepairReport", "提交报告参数 (COS): ${Gson().toJson(params)}")
        
        workOrderApi.submitReport(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                dialog.dismiss()
                android.util.Log.d("RepairReport", "提交报告响应: code=${response.code()} body=${Gson().toJson(response.body())}")
                if (response.isSuccessful && response.body()?.code == 200) {
                    // 注释掉清除草稿的代码，以便在撤回报告后能够恢复报告内容
                    // 与Vue项目保持一致，Vue项目提交报告后不清除草稿
                    // clearServerDraft()
                    
                    Toast.makeText(this@RepairReportEditActivity, "提交成功", Toast.LENGTH_SHORT).show()
                    setResult(Activity.RESULT_OK)
                    finish()
                } else {
                    val errorMsg = response.body()?.msg ?: "未知错误"
                    Toast.makeText(this@RepairReportEditActivity, "提交失败: $errorMsg", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                dialog.dismiss()
                android.util.Log.e("RepairReport", "提交报告网络错误: ${t.message}", t)
                Toast.makeText(this@RepairReportEditActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    

    
    /**
     * 清除服务器草稿
     * 通过提交一个空的草稿到服务器来实现
     */
    private fun clearServerDraft() {
        try {
            val engineerId = getSharedPreferences("token_pref", MODE_PRIVATE).getString("engineerId", "") ?: ""
            val emptyParams = HashMap<String, Any>()
            emptyParams["engineerId"] = engineerId
            emptyParams["workOrderId"] = orderId ?: ""
            emptyParams["excDesc"] = ""
            emptyParams["resolveDesc"] = ""
            emptyParams["announcements"] = ""
            emptyParams["excDescPics"] = emptyList<Any>()
            emptyParams["resolveDescPics"] = emptyList<Any>()
            emptyParams["replaceInfoList"] = emptyList<Any>()
            emptyParams["clearDraft"] = true  // 特殊标记，表示清除草稿
            
            workOrderApi.savePopReport(emptyParams).enqueue(object : Callback<ApiResponse<Any>> {
                override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                    if (response.isSuccessful && response.body()?.code == 200) {
                        android.util.Log.d("RepairReport", "服务器草稿已清除")
                    } else {
                        android.util.Log.e("RepairReport", "清除服务器草稿失败: ${response.body()?.msg}")
                    }
                }
                override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                    android.util.Log.e("RepairReport", "清除服务器草稿网络错误", t)
                }
            })
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "清除服务器草稿异常", e)
        }
    }

    /**
     * 查询客户自配零件列表
     */
    fun customerSearch(customerId: String, deviceGroupId: String, itemName: String = "") {
        val params = mapOf(
            "customerId" to customerId,
            "deviceGroupId" to deviceGroupId,
            "itemName" to itemName
        )
        workOrderApi.getCustomerItemStore(params).enqueue(object : retrofit2.Callback<ApiResponse<List<PartItem>>> {
            override fun onResponse(call: retrofit2.Call<ApiResponse<List<PartItem>>>, response: retrofit2.Response<ApiResponse<List<PartItem>>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val partList = response.body()?.data ?: emptyList()
                    customerParts.clear()
                    customerParts.addAll(partList)
                    customerPartAdapter.notifyDataSetChanged()
                } else {
                    Toast.makeText(this@RepairReportEditActivity, response.body()?.msg ?: "客户配件查询失败", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: retrofit2.Call<ApiResponse<List<PartItem>>>, t: Throwable) {
                Toast.makeText(this@RepairReportEditActivity, "客户配件查询失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 查询工程师领用零件列表
     */
    fun engineerSearch(deviceGroupId: String, itemName: String = "") {
        val params = mapOf(
            "deviceGroupId" to deviceGroupId,
            "itemName" to itemName
        )
        workOrderApi.getEngineerItemStore(params).enqueue(object : retrofit2.Callback<ApiResponse<List<PartItem>>> {
            override fun onResponse(call: retrofit2.Call<ApiResponse<List<PartItem>>>, response: retrofit2.Response<ApiResponse<List<PartItem>>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val partList = response.body()?.data ?: emptyList()
                    engineerParts.clear()
                    engineerParts.addAll(partList)
                    engineerPartAdapter.notifyDataSetChanged()
                } else {
                    Toast.makeText(this@RepairReportEditActivity, response.body()?.msg ?: "工程师配件查询失败", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: retrofit2.Call<ApiResponse<List<PartItem>>>, t: Throwable) {
                Toast.makeText(this@RepairReportEditActivity, "工程师配件查询失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 优化添加按钮的触摸范围
     */
    private fun optimizeAddButtonTouchArea(button: ImageView) {
        // 由于添加按钮已经有100dp的容器，不需要额外的最小尺寸限制
        
        // 添加适当的内边距
        val padding = (4 * resources.displayMetrics.density).toInt()
        button.setPadding(padding, padding, padding, padding)
        
        // 添加触摸反馈效果
        button.setOnTouchListener { v, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    v.alpha = 0.8f
                    v.scaleX = 0.95f
                    v.scaleY = 0.95f
                }
                android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                    v.alpha = 1.0f
                    v.scaleX = 1.0f
                    v.scaleY = 1.0f
                }
            }
            false
        }
        
        // 提升按钮的可访问性
        button.isClickable = true
        button.isFocusable = true
        
        // 由于容器已经100dp，轻微扩大触摸代理区域即可
        button.post {
            val parent = button.parent as? ViewGroup
            parent?.let {
                val delegateArea = android.graphics.Rect()
                button.getHitRect(delegateArea)
                
                // 轻微扩大触摸区域8dp
                val extraSpace = (8 * resources.displayMetrics.density).toInt()
                delegateArea.top -= extraSpace
                delegateArea.bottom += extraSpace
                delegateArea.left -= extraSpace
                delegateArea.right += extraSpace
                
                parent.touchDelegate = android.view.TouchDelegate(delegateArea, button)
            }
        }
    }

    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog(galleryRequestCode: Int, cameraRequestCode: Int) {
        val options = arrayOf("从相册选择", "拍照上传")
        
        AlertDialog.Builder(this)
            .setTitle("请选择图片来源")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> selectImagesFromGallery(galleryRequestCode)
                    1 -> takePhoto(cameraRequestCode)
                }
            }
            .show()
    }

    // 处理权限请求结果
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    if (pendingCameraRequestCode != 0) {
                        takePhoto(pendingCameraRequestCode)
                        pendingCameraRequestCode = 0
                    }
                } else {
                    Toast.makeText(this, "需要相机权限才能拍照上传", Toast.LENGTH_SHORT).show()
                }
                return
            }
            REQUEST_STORAGE_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    if (pendingGalleryRequestCode != 0) {
                        selectImagesFromGallery(pendingGalleryRequestCode)
                        pendingGalleryRequestCode = 0
                    }
                } else {
                    Toast.makeText(this, "需要存储权限才能读取相册", Toast.LENGTH_SHORT).show()
                }
                return
            }
        }
    }

    // 设置spinner的样式
    private fun setUpSpinnerStyles() {
        // 初始化所有下拉框
        setupClearableSpinner(spinnerPhenomenon) { 
            spinnerPhenomenon.setSelection(0)
        }
        
        setupClearableSpinner(spinnerReason) { 
            spinnerReason.setSelection(0)
        }
        
        setupClearableSpinner(spinnerHandleType) { 
            spinnerHandleType.setSelection(0)
        }
        
        setupClearableSpinner(spinnerBreakdown) { 
            spinnerBreakdown.setSelection(0)
        }
    }
    
    /**
     * 设置可清除的下拉框
     */
    private fun setupClearableSpinner(spinner: Spinner, onClear: () -> Unit) {
        // 初始默认选项
        val defaultItems = listOf("请选择")
        
        // 创建可清除的适配器
        val adapter = ClearableSpinnerAdapter(
            this,
            R.layout.spinner_clear_item,
            defaultItems,
            onClear
        )
        
        // 设置下拉样式
        adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
        
        // 设置到spinner
        spinner.adapter = adapter
    }

    // 带重试机制的 TransferManager 初始化
    private fun initTransferManagerWithRetry(retryCount: Int = 10, delayMillis: Long = 800) {
        val cosService = RepairOrderApp.cosService
        if (cosService == null) {
            if (retryCount > 0) {
                if (retryCount == 10) {
                    // Toast.makeText(this, "正在初始化COS服务，请稍候...", Toast.LENGTH_SHORT).show()
                }
                transferManagerInitHandler.postDelayed({
                    initTransferManagerWithRetry(retryCount - 1, delayMillis)
                }, delayMillis)
            } else {
                // Toast.makeText(this, "图片上传服务初始化失败，请稍后重试", Toast.LENGTH_LONG).show()
            }
            return
        }
        val transferConfig = TransferConfig.Builder().build()
        transferManager = TransferManager(cosService, transferConfig)
        android.util.Log.d("RepairReport", "TransferManager 初始化完成")
    }

    /**
     * 设置带有草稿数据的下拉框
     */
    private fun setUpSpinnerWithDraft(
        dictCode: String,
        spinner: Spinner,
        optionsList: List<Option>,
        selectedIndex: Int,
        fieldName: String
    ) {
        try {
            workOrderApi.getDictOptions(dictCode).enqueue(object : Callback<ApiResponse<List<Option>>> {
                override fun onResponse(call: Call<ApiResponse<List<Option>>>, response: Response<ApiResponse<List<Option>>>) {
                    if (response.isSuccessful && response.body()?.code == 200) {
                        val options = response.body()?.data ?: emptyList()
                        val fullOptions = listOf(Option("", "请选择")) + options
                        
                        // 根据字典代码更新对应的选项列表
                        when (dictCode) {
                            "6000" -> phenomenonOptions = fullOptions
                            "7000" -> reasonOptions = fullOptions
                            "8000" -> handleTypeOptions = fullOptions
                            "9000" -> breakdownOptions = fullOptions
                        }
                        
                        val adapter = ClearableSpinnerAdapter(
                            this@RepairReportEditActivity,
                            R.layout.spinner_clear_item,
                            fullOptions.map { it.label },
                            { spinner.setSelection(0) }
                        )
                        adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                        spinner.adapter = adapter
                        
                        // 恢复选中项
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            try {
                                if (selectedIndex > 0 && selectedIndex < spinner.adapter.count) {
                                    spinner.setSelection(selectedIndex)
                                    android.util.Log.d("RepairReport", "$fieldName 选择已恢复: $selectedIndex")
                                }
                            } catch (e: Exception) {
                                android.util.Log.e("RepairReport", "设置${fieldName}选择异常", e)
                            }
                        }, 500)
                    }
                }
                override fun onFailure(call: Call<ApiResponse<List<Option>>>, t: Throwable) {
                    android.util.Log.e("RepairReport", "加载${fieldName}选项失败", t)
                }
            })
        } catch (e: Exception) {
            android.util.Log.e("RepairReport", "${fieldName}恢复异常", e)
        }
    }

    /**
     * 确保图片上传完成后保存草稿
     */
    private fun ensureImagesSavedToServer(draftData: HashMap<String, Any?>) {
        // 过滤出需要上传的本地图片URI（排除虚拟URI）
        val localExcDescImageUris = excDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        val localResolveDescImageUris = resolveDescImageUris.filter { !it.toString().startsWith("content://server_image_") }
        
        // 如果没有本地图片需要上传，直接保存草稿
        if (localExcDescImageUris.isEmpty() && localResolveDescImageUris.isEmpty()) {
            android.util.Log.d("RepairReport", "没有本地图片需要上传，直接保存草稿")
            doSaveToServer(draftData, null)
            return
        }
        
        // 否则先上传本地图片
        android.util.Log.d("RepairReport", "检测到未上传的本地图片，先上传图片")
        uploadAllImagesCos(localExcDescImageUris, { newExcUrls ->
            uploadAllImagesCos(localResolveDescImageUris, { newResolveUrls ->
                // 使用统一的URL更新逻辑，避免重复
                updateImageUrls(newExcUrls, newResolveUrls)
                
                // 使用上传后的URL保存草稿
                android.util.Log.d("RepairReport", "图片上传完成，保存草稿")
                doSaveToServer(draftData, null)
                
            }, { error ->
                runOnUiThread {
                    android.util.Log.e("RepairReport", "解决措施图片上传失败: $error")
                }
            })
        }, { error ->
            runOnUiThread {
                android.util.Log.e("RepairReport", "故障描述图片上传失败: $error")
            }
        })
    }
} 